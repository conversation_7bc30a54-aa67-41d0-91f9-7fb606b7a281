package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.AdAccountInsightQuery;
import top.continew.admin.biz.model.query.AdAccountRetentionQuery;
import top.continew.admin.biz.model.resp.AdAccountDailyStatReportResp;
import top.continew.admin.biz.model.resp.AdAccountRetentionResp;
import top.continew.admin.biz.model.resp.AdAccountStatResp;
import top.continew.admin.biz.model.resp.DailyStatReportResp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/2/10 11:07
 */
@Mapper
public interface FinanceStatMapper {

    IPage<AdAccountStatResp> selectAdAccountStatPage(@Param("page") IPage<AdAccountInsightQuery> page,
                                                     @Param("query") AdAccountInsightQuery query);

    IPage<AdAccountDailyStatReportResp> selectAdAccountDailyStatReport(@Param("page") IPage<?> page,
                                                                       @Param("adAccountId") String adAccountId,
                                                                       @Param("start") LocalDateTime start,
                                                                       @Param("end") LocalDateTime end);

    IPage<DailyStatReportResp> selectDailyStatReportPage(@Param("page") IPage<CustomerDO> page,
                                                         @Param("start") LocalDateTime start,
                                                         @Param("end") LocalDateTime end,
                                                         @Param("customerId") Long customerId,
                                                         @Param("customerIndustry") Integer customerIndustry);

    List<DailyStatReportResp> selectDailyStatReportList(@Param("start") LocalDateTime start,
                                                        @Param("end") LocalDateTime end,
                                                        @Param("customerId") Long customerId,
                                                        @Param("customerIndustry") Integer customerIndustry);

    IPage<AdAccountRetentionResp> selectAdAccountRetentionPage(@Param("page") IPage<?> page,
                                                               @Param("query") AdAccountRetentionQuery query);

}
