/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.telegram.telegrambots.meta.api.methods.ParseMode;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.CustomerBalanceChangeEvent;
import top.continew.admin.biz.event.CustomerBalanceChangeModel;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.excel.OrderDailyExcelData;
import top.continew.admin.biz.mapper.AdAccountOrderMapper;
import top.continew.admin.biz.mapper.FbAdCampaignsMapper;
import top.continew.admin.biz.mapper.FbAdSetsMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.query.*;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.AdAccountHelper;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.data.mp.util.QueryWrapperHelper;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 下户订单业务实现
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdAccountOrderServiceImpl extends BaseServiceImpl<AdAccountOrderMapper, AdAccountOrderDO, AdAccountOrderResp, AdAccountOrderDetailResp, AdAccountOrderQuery, AdAccountOrderReq> implements AdAccountOrderService {

    private final AdAccountService adAccountService;

    private final CustomerService customerService;

    private final RechargeOrderService rechargeOrderService;

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final TagRelationService tagRelationService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    private final AdAccountHelper adAccountHelper;

    private final CardTransactionService cardTransactionService;

    private final FbAdCampaignsMapper fbAdCampaignsMapper;

    private final FbAdSetsMapper fbAdSetsMapper;

    private final ProfitTypeService profitTypeService;
    private final UserService userService;

    @Override
    public void export(AdAccountOrderQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<AdAccountOrderDetailResp> list = this.list(query, sortQuery, this.getDetailClass());
        list.forEach(this::fill);
        for (AdAccountOrderDetailResp adAccountOrderDetailResp : list) {
            if (null != adAccountOrderDetailResp.getBmType()) {
                ProfitTypeDO profitType = profitTypeService.getById(adAccountOrderDetailResp.getBmType());
                adAccountOrderDetailResp.setBmTypeName(null != profitType ? profitType.getName() : null);
            }
        }
        ExcelUtils.export(list, "导出数据", this.getDetailClass(), response);
    }

    @Override
    protected void beforeAdd(AdAccountOrderReq req) {
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(req.getAdAccountId());
        CheckUtils.throwIfNull(adAccount, "广告户不存在:" + req.getAdAccountId());
        CheckUtils.throwIf(adAccount.getSaleStatus().equals(AdAccountSaleStatusEnum.SALT) || adAccount.getSaleStatus()
            .equals(AdAccountSaleStatusEnum.SALEING), "请勿重复出售:" + req.getAdAccountId());
        CheckUtils.throwIf(ObjectUtils.isEmpty(req.getCustomerBmId()) &&
                ObjectUtils.isEmpty(req.getCustomerEmail()), "客户BM ID和客户邮箱必填其中一个");
        AdAccountOrderDO adAccountOrder = this.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getAdAccountId, req.getAdAccountId())
            .in(AdAccountOrderDO::getStatus, List.of(AdAccountOrderStatusEnum.PENDING, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.PROCESS)));
        CheckUtils.throwIfNotNull(adAccountOrder, "请先取消之前的下户订单:" + req.getAdAccountId());
        CustomerDO customer = customerService.getById(req.getCustomerId());
        customer.setBusinessUserId(customer.getBusinessUserId());
    }

    @Override
    public synchronized Long add(AdAccountOrderReq req) {
        this.beforeAdd(req);
        AdAccountOrderDO entity = BeanUtil.copyProperties(req, super.getEntityClass());
        entity.setOrderNo(CommonUtils.randomOrderNo(""));
        entity.setPayTime(LocalDateTime.now());
        entity.setStatus(AdAccountOrderStatusEnum.PENDING);
        this.save(entity);
        this.afterAdd(req, entity);
        return entity.getId();
    }

    @Override
    protected void afterAdd(AdAccountOrderReq req, AdAccountOrderDO entity) {
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.SALEING)
            .eq(AdAccountDO::getPlatformAdId, entity.getAdAccountId()));
    }

    @Override
    protected void beforeUpdate(AdAccountOrderReq req, Long id) {
        CheckUtils.throwIf(ObjectUtils.isEmpty(req.getCustomerBmId()) &&
                ObjectUtils.isEmpty(req.getCustomerEmail()), "客户BM ID和客户邮箱必填其中一个");
        super.beforeUpdate(req, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddByAdAccountId(AdAccountOrderBatchAddByAdAccountIdReq req) {
        int size = req.getAdAccountIds().size();

        CheckUtils.throwIf(CollUtil.isNotEmpty(req.getAdAccountNames()) && size != req.getAdAccountNames()
            .size(), "广告户ID和名称数量不一致");
        CheckUtils.throwIf(ObjectUtils.isEmpty(req.getCustomerBmId()) &&
                ObjectUtils.isEmpty(req.getCustomerEmail()), "客户BM ID和客户邮箱必填其中一个");
        CustomerDO customer = customerService.getById(req.getCustomerId());

        for (int i = 0; i < size; i++) {
            AdAccountOrderReq orderReq = new AdAccountOrderReq();
            BeanUtil.copyProperties(req, orderReq);
            orderReq.setBusinessUserId(customer.getBusinessUserId());
            orderReq.setAdAccountId(req.getAdAccountIds().get(i));
            if (CollUtil.isNotEmpty(req.getAdAccountNames())) {
                orderReq.setAdAccountName(req.getAdAccountNames().get(i));
            }
            add(orderReq);
        }
    }

    @Override
    public PageResp<AdAccountOrderResp> page(AdAccountOrderQuery query, PageQuery pageQuery) {
        QueryWrapper<AdAccountOrderDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, pageQuery);
        IPage<AdAccountOrderResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        for (AdAccountOrderResp record : page.getRecords()) {
            if (record.getStatus().equals(AdAccountOrderStatusEnum.AUTH_COMPLETED)) {
                if (record.getSpendCap() != null) {
                    record.setFbBalance(record.getEnablePrepay() ? record.getRechargeAmount()
                        .subtract(record.getTotalSpent()) : record.getSpendCap().subtract(record.getAmountSpent()));
                }
                if (record.getStartCampaignTime() == null) {
                    record.setCampaignDesc("未开启广告系列");
                } else {
                    long betweenDay = LocalDateTimeUtil.between(record.getStartCampaignTime(), LocalDateTime.now(), ChronoUnit.DAYS);
                    record.setCampaignDesc("已开启广告系列%d天".formatted(betweenDay));
                }

            }

            if (null != record.getUsedUserId() && record.getUsedUserId() > 0L) {
                record.setUsedUserName(userService.getNickName(record.getUsedUserId()));
            }
        }
        return PageResp.build(page);
    }

    @Override
    protected QueryWrapper<AdAccountOrderDO> buildQueryWrapper(AdAccountOrderQuery query) {
        QueryWrapper<AdAccountOrderDO> queryWrapper = new QueryWrapper<>();
        QueryWrapperHelper.build(query, this.getQueryFields(), queryWrapper);
        if (StrUtil.isNotBlank(query.getRemark())) {
            if ("空".equals(query.getRemark())) {
                queryWrapper.eq("o.remark", StrUtil.EMPTY);
            } else {
                queryWrapper.like("o.remark", query.getRemark());
            }
        }
        if (StringUtils.isNotBlank(query.getAdAccountBmIds())) {
            if ("空".equals(query.getAdAccountBmIds())) {
                queryWrapper.eq("a.bm_id", StrUtil.EMPTY);
            } else {
                queryWrapper.in("a.bm_id", StrUtil.split(query.getAdAccountBmIds()
                    .replace("\n", ",")
                    .replace(" ", ",")
                    .replace("，", ","), ","));
            }
        }

        if (StringUtils.isNotBlank(query.getAdAccountIds())) {
            if ("空".equals(query.getAdAccountIds())) {
                queryWrapper.eq("o.ad_account_id", StrUtil.EMPTY);
            } else {
                List<String> adAccountIds = StrUtil.split(query.getAdAccountIds()
                    .replace("\n", ",")
                    .replace(" ", ",")
                    .replace("，", ","), ",");
                queryWrapper.in("o.ad_account_id", adAccountIds);
                queryWrapper.orderBy(true, true, "FIELD(o.ad_account_id, '" + String.join("','", adAccountIds) + "')");
            }
        }

        if (query.getCampaignTime() != null) {
            LocalDateTime localDateTime = LocalDateTime.now().minusDays(query.getCampaignTime());
            queryWrapper.ge("o.start_campaign_time", localDateTime);
        }
        if (query.getIsOpen() != null) {
            if (query.getIsOpen()) {
                queryWrapper.isNotNull("o.start_campaign_time");
            } else {
                queryWrapper.isNull("o.start_campaign_time");
            }
        }
        if (CollUtil.isNotEmpty(query.getTags())) {
            List<Long> orderIds = tagRelationService.listOrderId(query.getTags());
            if (orderIds.isEmpty()) {
                queryWrapper.eq("o.id", -1);
            } else {
                queryWrapper.in("o.id", orderIds);
            }
        }
        return queryWrapper;
    }

    @Override
    protected void sort(QueryWrapper<AdAccountOrderDO> queryWrapper, SortQuery sortQuery) {
        if (sortQuery != null && !sortQuery.getSort().isUnsorted()) {
            Sort sort = sortQuery.getSort();

            for (Sort.Order order : sort) {
                String property = order.getProperty();
                queryWrapper.orderBy(true, order.isAscending(), CharSequenceUtil.toUnderlineCase(property));
            }

        }
    }

    @Override
    protected <E> List<E> list(AdAccountOrderQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<AdAccountOrderDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<AdAccountOrderResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        for (AdAccountOrderResp record : entityList) {
            if (record.getStatus().equals(AdAccountOrderStatusEnum.AUTH_COMPLETED)) {
                if (record.getSpendCap() != null) {
                    record.setFbBalance(record.getEnablePrepay() ? record.getRechargeAmount()
                        .subtract(record.getAmountSpent()) : record.getSpendCap().subtract(record.getAmountSpent()));
                }
            }
        }
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(MultipartFile file, Long customerId) {
        CheckUtils.throwIfNull(customerId, "客户不能为空");
        List<OrderDailyExcelData> importRowList;
        try {
            importRowList = EasyExcel.read(file.getInputStream())
                .head(OrderDailyExcelData.class)
                .sheet("开户记录")
                .doReadSync();
        } catch (IOException e) {
            log.error("开户记录数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }
        List<CustomerDailyReportImportRowReq> dailyReportRowList;
        // 读取表格数据
        try {
            dailyReportRowList = EasyExcel.read(file.getInputStream())
                .head(CustomerDailyReportImportRowReq.class)
                .sheet("日报")
                .headRowNumber(3)
                .doReadSync();
        } catch (Exception e) {
            log.error("日报数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }
        Map<String, BigDecimal> adAccountBuyAmountMap = new HashMap<>();
        for (CustomerDailyReportImportRowReq customerDailyReportImportRowReq : dailyReportRowList) {
            if (StringUtils.isBlank(customerDailyReportImportRowReq.getPlatformAdId())) {
                continue;
            }
            if (customerDailyReportImportRowReq.getBuyAdAccountAmount() != null) {
                adAccountBuyAmountMap.put(customerDailyReportImportRowReq.getPlatformAdId(), customerDailyReportImportRowReq.getBuyAdAccountAmount());
            }
        }
        List<AdAccountOrderDO> existList = this.list(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getCustomerId, customerId));
        List<String> existPlatformAdId = existList.stream().map(AdAccountOrderDO::getAdAccountId).toList();
        List<AdAccountOrderDO> saveList = new ArrayList<>();
        for (OrderDailyExcelData orderDailyExcelData : importRowList) {
            if (existPlatformAdId.contains(orderDailyExcelData.getAccountId())) {
                continue;
            }
            AdAccountOrderDO adAccountOrderDO = new AdAccountOrderDO();
            adAccountOrderDO.setOrderNo(CommonUtils.randomOrderNo(""));
            adAccountOrderDO.setCustomerId(customerId);
            adAccountOrderDO.setCustomerBmId(orderDailyExcelData.getCustomerBm());
            adAccountOrderDO.setAdAccountId(orderDailyExcelData.getAccountId());
            adAccountOrderDO.setPayAmount(adAccountBuyAmountMap.getOrDefault(orderDailyExcelData.getAccountId(), BigDecimal.ZERO));
            LocalDateTime payTime = LocalDateTimeUtil.of(DateUtil.parse(orderDailyExcelData.getDate()));
            adAccountOrderDO.setPayTime(payTime);
            adAccountOrderDO.setStatus(AdAccountOrderStatusEnum.AUTH_COMPLETED);
            adAccountOrderDO.setRemark(orderDailyExcelData.getRemark());
            adAccountOrderDO.setAdAccountName(orderDailyExcelData.getAdAccountName());
            saveList.add(adAccountOrderDO);
        }
        saveBatch(saveList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void batchAddByBmType(AdAccountOrderBatchAddByBmTypeReq req) {
        CheckUtils.throwIfNull(req.getBmType(), "bm类型不能为空");
        CheckUtils.throwIf(ObjectUtils.isEmpty(req.getCustomerBmId()) &&
                ObjectUtils.isEmpty(req.getCustomerEmail()), "客户BM ID和客户邮箱必填其中一个");
        List<AdAccountDO> accounts = adAccountService.selectWaitSaleAdAccountList(req.getTimeZone(), req.getUseCleanBm5(), req.getIsLowLimit(), req.getRequireVo(), LocalDateTime.now()
            .minusHours(22), req.getBmType());

        if (accounts.size() < req.getPurchasedQuantity()) {
            throw new BusinessException(req.getTimeZone() + "下的广告户不足");
        }
        boolean notBlank = StringUtils.isNotBlank(req.getAdAccountName());
        String[] names = new String[0];
        if (notBlank) {
            names = req.getAdAccountName().replace("，", ",").split(",");
            if (names.length != req.getPurchasedQuantity()) {
                throw new BusinessException("广告户名字数量和购买数量不匹配");
            }
        }

        CustomerDO customer = customerService.getById(req.getCustomerId());

        List<AdAccountOrderDO> saveList = new ArrayList<>();
        List<AdAccountDO> updateList = new ArrayList<>();
        for (int i = 0; i < req.getPurchasedQuantity(); i++) {
            AdAccountDO account = accounts.get(i);
            AdAccountOrderDO adAccountOrder = this.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
                .eq(AdAccountOrderDO::getAdAccountId, account.getPlatformAdId())
                .in(AdAccountOrderDO::getStatus, List.of(AdAccountOrderStatusEnum.PENDING, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.PROCESS)));
            CheckUtils.throwIfNotNull(adAccountOrder, "请先取消之前的下户订单:" + account.getPlatformAdId());
            AdAccountOrderDO adAccountOrderDO = new AdAccountOrderDO();
            adAccountOrderDO.setOrderNo(CommonUtils.randomOrderNo(""));
            adAccountOrderDO.setCustomerId(req.getCustomerId());
            adAccountOrderDO.setBusinessUserId(customer.getBusinessUserId());
            adAccountOrderDO.setCustomerBmId(req.getCustomerBmId());
            adAccountOrderDO.setAdAccountId(account.getPlatformAdId());
            adAccountOrderDO.setPayTime(req.getPayTime() == null ? LocalDateTime.now() : req.getPayTime());
            adAccountOrderDO.setAdAccountName(notBlank ? names[i] : "");
            adAccountOrderDO.setStatus(AdAccountOrderStatusEnum.PENDING);
            adAccountOrderDO.setPayAmount(req.getPayAmount());
            adAccountOrderDO.setCustomerRequirementId(req.getCustomerRequirementId());
            adAccountOrderDO.setRemark(req.getRemark());
            saveList.add(adAccountOrderDO);
            AdAccountDO update = new AdAccountDO();
            update.setId(account.getId());
            update.setSaleStatus(AdAccountSaleStatusEnum.SALEING);
            if (req.getBm1Id() != null) {
                update.setBm1Id(req.getBm1Id());
            }
            updateList.add(update);
        }
        saveBatch(saveList);
        adAccountService.updateBatchById(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void handle(Long id) {

        AdAccountOrderDO accountOrder = getById(id);
        CheckUtils.throwIfNull(accountOrder, "没找到对应的订单");
        CheckUtils.throwIf(!accountOrder.getStatus().equals(AdAccountOrderStatusEnum.PENDING), "当前订单不是待处理");
        update(new LambdaUpdateWrapper<AdAccountOrderDO>().eq(AdAccountOrderDO::getId, id)
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.PROCESS)
            .set(AdAccountOrderDO::getHandleUser, StpUtil.getLoginIdAsLong())
            .set(AdAccountOrderDO::getHandleTime, LocalDateTime.now()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void cancel(Long id) {
        AdAccountOrderDO accountOrder = getById(id);
        CheckUtils.throwIfNull(accountOrder, "没找到对应的订单");
        CheckUtils.throwIf(!accountOrder.getStatus()
            .equals(AdAccountOrderStatusEnum.PENDING) && !accountOrder.getStatus()
            .equals(AdAccountOrderStatusEnum.PROCESS), "当前订单状态无法取消");
        update(new LambdaUpdateWrapper<AdAccountOrderDO>().eq(AdAccountOrderDO::getId, id)
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.CANCEL)
            .set(AdAccountOrderDO::getEndTime, LocalDateTime.now()));
        // 广告户状态恢复出售状态
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.WAIT)
            .set(AdAccountDO::getSaleTime, null)
            .eq(AdAccountDO::getPlatformAdId, accountOrder.getAdAccountId()));
    }

    @Override
    public synchronized void batchAuthorize(IdsReq req) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refund(Long id) {
        AdAccountOrderDO order = this.getById(id);
        CheckUtils.throwIfNull(order, "订单不存在");

        // 检查订单状态是否为授权完成(3)或回收(5)
        CheckUtils.throwIf(!AdAccountOrderStatusEnum.AUTH_COMPLETED.equals(order.getStatus()) && !AdAccountOrderStatusEnum.RECYCLE.equals(order.getStatus()), "只有授权完成或回收状态的订单才能退款");

        // 检查是否已退款
        CheckUtils.throwIf(Boolean.TRUE.equals(order.getRefunded()), "该订单已退款");

        // 检查开户费是否大于0
        CheckUtils.throwIf(order.getPayAmount() == null || order.getPayAmount()
            .compareTo(BigDecimal.ZERO) <= 0, "开户费必须大于0才能退款");

        // 更新订单为已退款
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getRefunded, true)
            .eq(AdAccountOrderDO::getId, id));
        BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(order.getCustomerId());
        CustomerDO customer = customerService.getById(customerService.getById(order.getCustomerId()));
        // 给客户退款
        customerService.changeAmount(order.getCustomerId(), order.getAdAccountId(), order.getPayAmount(), CustomerBalanceTypeEnum.AD_ACCOUNT_REFUND, LocalDateTime.now(), "");
        CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, null, customer.getBalance(), order.getPayAmount());
        SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
    }

    @Override
    public PageResp<UnSpentOrderResp> selectUnSpentOrderPage(InactiveAccountAnalyzeQuery query) {
        // 设置默认值
        if (query.getDay() == null) {
            query.setDay(7);
        }
        if (query.getSpentLimit() == null) {
            query.setSpentLimit(10);
        }
        if (query.getOrderDaysBefore() == null) {
            query.setOrderDaysBefore(7);
        }

        // 计算下户时间范围
        LocalDateTime endTime = LocalDateTime.now().minusDays(query.getOrderDaysBefore());

        QueryWrapper<AdAccountOrderDO> wrapper = new QueryWrapper<>();
        wrapper.eq("o.status", AdAccountOrderStatusEnum.AUTH_COMPLETED)
            .le("o.finish_time", endTime)
            .eq("a.account_status", AdAccountStatusEnum.NORMAL)
            .eq("a.usable", true)
            .le("COALESCE(account_spent.insight_spend, 0)", query.getSpentLimit());

        // 现有筛选条件
        wrapper.eq(StringUtils.isNotBlank(query.getPlatformAdId()), "o.ad_account_id", query.getPlatformAdId());
        wrapper.eq(StringUtils.isNotBlank(query.getTimezone()), "a.timezone", query.getTimezone());
        wrapper.eq(query.getCustomerId() != null, "o.customer_id", query.getCustomerId());
        wrapper.eq(query.getClearStatus() != null, "o.clear_status", query.getClearStatus());
        if (query.getIsStartCampaign() != null) {
            if (query.getIsStartCampaign()) {
                wrapper.isNotNull("o.start_campaign_time");
            } else {
                wrapper.isNull("o.start_campaign_time");
            }
        }

        // 新增：账号类型筛选
        if (query.getBmItemType() != null && !query.getBmItemType().isEmpty()) {
            wrapper.in("a.bm_item_type", query.getBmItemType());
        }

        // 新增：BM授权时间筛选
        if (query.getBmAuthTime() != null && query.getBmAuthTime().size() == 2) {
            String startTime = query.getBmAuthTime().get(0);
            String endTime2 = query.getBmAuthTime().get(1);
            if (startTime != null) {
                wrapper.ge("a.bm_auth_time", startTime);
            }
            if (endTime2 != null) {
                wrapper.le("a.bm_auth_time", endTime2);
            }
        }

        if (query.getSort() != null && query.getSort().isSorted()) {
            Sort.Order order = query.getSort().stream().findFirst().get();
            if (order.isAscending()) {
                wrapper.orderByAsc(StrUtil.toUnderlineCase(order.getProperty()));
            } else {
                wrapper.orderByDesc(StrUtil.toUnderlineCase(order.getProperty()));
            }
        }
        IPage<UnSpentOrderResp> page = this.baseMapper.selectNoSpentOrderPage(new Page<>(query.getPage(), query.getSize()), query.getDay(), wrapper);

        for (UnSpentOrderResp record : page.getRecords()) {
            if (record.getStartCampaignTime() == null) {
                record.setCampaignDesc("未开启广告系列");
            } else {
                long betweenDay = LocalDateTimeUtil.between(record.getStartCampaignTime(), LocalDateTime.now(), ChronoUnit.DAYS);
                record.setCampaignDesc("已开启广告系列%d天".formatted(betweenDay));
            }
        }

        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<UnSpentOrderResp> selectUnSpentOrderList(InactiveAccountAnalyzeQuery query) {
        // 设置默认值
        if (query.getDay() == null) {
            query.setDay(7);
        }
        if (query.getSpentLimit() == null) {
            query.setSpentLimit(10);
        }
        if (query.getOrderDaysBefore() == null) {
            query.setOrderDaysBefore(7);
        }

        // 计算下户时间范围
        LocalDateTime endTime = LocalDateTime.now().minusDays(query.getOrderDaysBefore());

        QueryWrapper<AdAccountOrderDO> wrapper = new QueryWrapper<>();
        wrapper.eq("o.status", AdAccountOrderStatusEnum.AUTH_COMPLETED)
            .le("o.finish_time", endTime)
            .eq("a.account_status", AdAccountStatusEnum.NORMAL)
            .eq("a.usable", true)
            .le("COALESCE(account_spent.insight_spend, 0)", query.getSpentLimit());

        // 现有筛选条件
        wrapper.eq(StringUtils.isNotBlank(query.getPlatformAdId()), "o.ad_account_id", query.getPlatformAdId());
        wrapper.eq(StringUtils.isNotBlank(query.getTimezone()), "a.timezone", query.getTimezone());
        wrapper.eq(query.getCustomerId() != null, "o.customer_id", query.getCustomerId());
        wrapper.eq(query.getClearStatus() != null, "o.clear_status", query.getClearStatus());
        if (query.getIsStartCampaign() != null) {
            if (query.getIsStartCampaign()) {
                wrapper.isNotNull("o.start_campaign_time");
            } else {
                wrapper.isNull("o.start_campaign_time");
            }
        }
        // 新增：账号类型筛选
        if (query.getBmItemType() != null && !query.getBmItemType().isEmpty()) {
            wrapper.in("a.bm_item_type", query.getBmItemType());
        }

        // 新增：BM授权时间筛选
        if (query.getBmAuthTime() != null && query.getBmAuthTime().size() == 2) {
            String startTime = query.getBmAuthTime().get(0);
            String endTime2 = query.getBmAuthTime().get(1);
            if (startTime != null) {
                wrapper.ge("a.bm_auth_time", startTime);
            }
            if (endTime2 != null) {
                wrapper.le("a.bm_auth_time", endTime2);
            }
        }

        List<UnSpentOrderResp> res = this.baseMapper.selectNoSpentOrderList(query.getDay(), wrapper);
        // 处理清零时间
        for (UnSpentOrderResp unSpentOrderResp : res) {
            if (unSpentOrderResp.getStartCampaignTime() == null) {
                unSpentOrderResp.setCampaignDesc("未开启广告系列");
            } else {
                long betweenDay = LocalDateTimeUtil.between(unSpentOrderResp.getStartCampaignTime(), LocalDateTime.now(), ChronoUnit.DAYS);
                unSpentOrderResp.setCampaignDesc("已开启广告系列%d天".formatted(betweenDay));
            }
        }
        return res;
    }

    @Override
    public PageResp<InsufficientBalanceResp> selectInsufficientBalanceOrderPage(InsufficientBalanceAdAccountQuery query,
                                                                                PageQuery pageQuery) {
        QueryWrapper<AdAccountOrderDO> wrapper = new QueryWrapper<>();
        wrapper.eq("o.status", AdAccountOrderStatusEnum.AUTH_COMPLETED)
            .ne("a.account_status", AdAccountStatusEnum.BANNED)
            .eq("o.clear_status", AdAccountClearStatusEnum.WAIT)
            .leSql("account_balance.card_balance", "account_spent.card_spent")
            .orderByAsc("card_balance");
        wrapper.eq(StringUtils.isNotBlank(query.getPlatformAdId()), "o.ad_account_id", query.getPlatformAdId());
        wrapper.eq(query.getCustomerId() != null, "o.customer_id", query.getCustomerId());
        IPage<InsufficientBalanceResp> page = this.baseMapper.selectInsufficientBalanceOrderPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), wrapper);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<AdAccountStatisticsResp> selectStatistics(AdAccountStatisticsQuery query) {

        return baseMapper.selectStatistics(query.getStatTimes());
    }

    @Override
    public synchronized void authorizeSuccess(Long id) {
        AdAccountOrderDO adAccountOrderDO = this.getById(id);
        CheckUtils.throwIf(!adAccountOrderDO.getStatus()
            .equals(AdAccountOrderStatusEnum.PROCESS), "当前订单状态不在进行中");
        CheckUtils.throwIf(!adAccountOrderDO.getHandleUser().equals(UserContextHolder.getUserId()), "仅限处理人处理");
        AdAccountDO accountDO = adAccountService.getByPlatformAdId(adAccountOrderDO.getAdAccountId());
        boolean isFinanceAccount = false;
        if (accountDO.getBmItemType() != null) {
            isFinanceAccount = profitTypeService.selectFinanceTypeIds().contains(accountDO.getBmItemType());
        }
        boolean isIgnoreRecharge = adAccountOrderDO.getCustomerId() == 668446538338750726L || adAccountOrderDO.getCustomerId() == 697414798966523150L || adAccountOrderDO.getOrderMethod() == 2 || isFinanceAccount;
        if (!isIgnoreRecharge) {
            List<RechargeOrderDO> rechargeOrder = rechargeOrderService.list(Wrappers.<RechargeOrderDO>lambdaQuery()
                .eq(RechargeOrderDO::getPlatformAdId, adAccountOrderDO.getAdAccountId())
                .eq(RechargeOrderDO::getStatus, RechargeOrderStatusEnum.FINISH));
            CheckUtils.throwIf(rechargeOrder.isEmpty(), "请先进行充值，再确认授权完成");
        }
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED)
            .set(AdAccountOrderDO::getFinishTime, LocalDateTime.now())
            .eq(AdAccountOrderDO::getId, adAccountOrderDO.getId()));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.SALT)
            .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.WAIT)
            .set(AdAccountDO::getSaleTime, LocalDateTime.now())
            .eq(AdAccountDO::getPlatformAdId, adAccountOrderDO.getAdAccountId()));
        if (adAccountOrderDO.getPayAmount() != null && adAccountOrderDO.getPayAmount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(adAccountOrderDO.getCustomerId());
            CustomerDO customer = customerService.getById(customerService.getById(adAccountOrderDO.getCustomerId()));
            customerService.changeAmount(adAccountOrderDO.getCustomerId(), adAccountOrderDO.getAdAccountId(), adAccountOrderDO.getPayAmount()
                .negate(), CustomerBalanceTypeEnum.AD_ACCOUNT_BUY, null, "");

            CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, null, customer.getBalance(), adAccountOrderDO.getPayAmount()
                .negate());
            SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
        }

    }

    @Override
    public void authorizeSuccessByRecharge(String platformAdId) {
        AdAccountOrderDO adAccountOrderDO = this.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getAdAccountId, platformAdId)
            .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.PROCESS));
        if (adAccountOrderDO == null) {
            return;
        }
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED)
            .set(AdAccountOrderDO::getFinishTime, LocalDateTime.now())
            .eq(AdAccountOrderDO::getId, adAccountOrderDO.getId()));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.SALT)
            .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.WAIT)
            .set(AdAccountDO::getSaleTime, LocalDateTime.now())
            .eq(AdAccountDO::getPlatformAdId, adAccountOrderDO.getAdAccountId()));
        if (adAccountOrderDO.getPayAmount() != null && adAccountOrderDO.getPayAmount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(adAccountOrderDO.getCustomerId());
            CustomerDO customer = customerService.getById(customerService.getById(adAccountOrderDO.getCustomerId()));
            customerService.changeAmount(adAccountOrderDO.getCustomerId(), adAccountOrderDO.getAdAccountId(), adAccountOrderDO.getPayAmount()
                .negate(), CustomerBalanceTypeEnum.AD_ACCOUNT_BUY, null, "");

            CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, null, customer.getBalance(), adAccountOrderDO.getPayAmount()
                .negate());
            SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
        }
    }

    @Override
    public void authorizeFail(Long id) {
        AdAccountOrderDO adAccountOrderDO = this.getById(id);
        CheckUtils.throwIf(!adAccountOrderDO.getStatus()
            .equals(AdAccountOrderStatusEnum.PROCESS), "当前订单状态不在进行中");
        CheckUtils.throwIf(!adAccountOrderDO.getHandleUser().equals(UserContextHolder.getUserId()), "仅限处理人处理");
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.SELF_AUTH_FAIL)
            .set(AdAccountOrderDO::getFinishTime, LocalDateTime.now())
            .eq(AdAccountOrderDO::getId, adAccountOrderDO.getId()));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.WAIT)
            .eq(AdAccountDO::getPlatformAdId, adAccountOrderDO.getAdAccountId()));
    }

    @Override
    public void receiveFail(Long id) {
        AdAccountOrderDO adAccountOrderDO = this.getById(id);
        CheckUtils.throwIf(!adAccountOrderDO.getStatus()
            .equals(AdAccountOrderStatusEnum.PROCESS), "当前订单状态不在进行中");
        CheckUtils.throwIf(!adAccountOrderDO.getHandleUser().equals(UserContextHolder.getUserId()), "仅限处理人处理");
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.RECEIVE_FAIL)
            .set(AdAccountOrderDO::getFinishTime, LocalDateTime.now())
            .eq(AdAccountOrderDO::getId, adAccountOrderDO.getId()));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.RECYCLE)
            .eq(AdAccountDO::getPlatformAdId, adAccountOrderDO.getAdAccountId()));
    }

    @Override
    public void invalid(Long id) {
        AdAccountOrderDO adAccountOrderDO = this.getById(id);
        CheckUtils.throwIf(!adAccountOrderDO.getStatus()
            .equals(AdAccountOrderStatusEnum.PROCESS), "当前订单状态不在进行中");
        CheckUtils.throwIf(!adAccountOrderDO.getHandleUser().equals(UserContextHolder.getUserId()), "仅限处理人处理");
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.INVALID)
            .set(AdAccountOrderDO::getFinishTime, LocalDateTime.now())
            .eq(AdAccountOrderDO::getId, adAccountOrderDO.getId()));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.WAIT)
            .eq(AdAccountDO::getPlatformAdId, adAccountOrderDO.getAdAccountId()));
    }

    @Override
    public void cancelRecycle(Long id) {
        AdAccountOrderDO adAccountOrderDO = this.getById(id);
        CheckUtils.throwIf(!adAccountOrderDO.getStatus()
            .equals(AdAccountOrderStatusEnum.RECYCLE), "当前订单状态不在回收中");
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(adAccountOrderDO.getAdAccountId());
        CheckUtils.throwIf(!adAccount.getSaleStatus().equals(AdAccountSaleStatusEnum.WAIT) && !adAccount.getSaleStatus()
            .equals(AdAccountSaleStatusEnum.RECYCLE), "广告户不处于待出售状态，无法回收");
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED)
            .set(AdAccountOrderDO::getRecycleTime, null)
            .eq(AdAccountOrderDO::getId, adAccountOrderDO.getId()));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.SALT)
            .set(AdAccountDO::getSaleTime, adAccountOrderDO.getFinishTime())
            .eq(AdAccountDO::getPlatformAdId, adAccountOrderDO.getAdAccountId()));
    }

    @Override
    public synchronized void batchChangeBmId(AdAccountOrderUpdateBmIdReq req) {
        CheckUtils.throwIf(ObjectUtils.isEmpty(req.getBmId()) &&
                ObjectUtils.isEmpty(req.getCustomerEmail()), "客户BM ID和客户邮箱必填其中一个");
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(ObjectUtils.isNotEmpty(req.getBmId()),AdAccountOrderDO::getCustomerBmId, req.getBmId())
            .set(ObjectUtils.isNotEmpty(req.getCustomerEmail()),AdAccountOrderDO::getCustomerEmail,req.getCustomerEmail())
            .in(AdAccountOrderDO::getId, req.getIds())
            .in(AdAccountOrderDO::getStatus, List.of(AdAccountOrderStatusEnum.PENDING, AdAccountOrderStatusEnum.PROCESS)));
    }

    @Override
    public void checkExistOrder(Long customerId, String platformAdId) {
        boolean exist = this.exists(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getCustomerId, customerId)
            .eq(AdAccountOrderDO::getAdAccountId, platformAdId)
            .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.PROCESS));
        CheckUtils.throwIf(!exist, "下户记录不存在");
    }

    @Override
    public BasePageResp<AdAccountSalesStatisticsResp> pageAdAccountSalesStatistics(AdAccountSalesStatisticsQuery query,
                                                                                   PageQuery pageQuery) {
        LocalDate start = null;
        LocalDate end = null;
        if (ObjectUtils.isNotEmpty(query.getStatTime()) && query.getStatTime().length == 2) {
            start = query.getStatTime()[0];
            end = query.getStatTime()[1];
        }
        IPage<AdAccountSalesStatisticsResp> page = baseMapper.selectAdAccountSalesStatistics(new Page<>((long)pageQuery.getPage(), (long)pageQuery.getSize()), start, end);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<AdAccountSalesStatisticsResp> listAdAccountSalesStatistics(AdAccountSalesStatisticsQuery query) {
        LocalDate start = null;
        LocalDate end = null;
        if (ObjectUtils.isNotEmpty(query.getStatTime()) && query.getStatTime().length == 2) {
            start = query.getStatTime()[0];
            end = query.getStatTime()[1];
        }
        return baseMapper.listAdAccountSalesStatistics(start, end);
    }

    @Override
    public AdAccountSalesStatisticsResp getAdAccountSalesStatisticsSummary(AdAccountSalesStatisticsQuery query) {
        LocalDate start = null;
        LocalDate end = null;
        if (ObjectUtils.isNotEmpty(query.getStatTime()) && query.getStatTime().length == 2) {
            start = query.getStatTime()[0];
            end = query.getStatTime()[1];
        }
        return this.baseMapper.getAdAccountSalesStatisticsSummary(start, end);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recycle(Long id) {
        AdAccountOrderDO adAccountOrderDO = getById(id);
        CheckUtils.throwIfNull(adAccountOrderDO, "没有找到对应的订单");
        CheckUtils.throwIf(adAccountOrderDO.getStatus() != AdAccountOrderStatusEnum.AUTH_COMPLETED, "该订单不是授权成功");
        adAccountService.update(new LambdaUpdateWrapper<AdAccountDO>().eq(AdAccountDO::getPlatformAdId, adAccountOrderDO.getAdAccountId())
            .set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.RECYCLE)
            .set(AdAccountDO::getSaleTime, null));
        update(new LambdaUpdateWrapper<AdAccountOrderDO>().eq(AdAccountOrderDO::getId, adAccountOrderDO.getId())
            .set(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.RECYCLE)
            .set(AdAccountOrderDO::getRecycleTime, LocalDateTime.now()));
    }

    @Override
    public Integer calAccountOrderTotalSpent(AdAccountOrderStatusEnum statusEnum) {
        return baseMapper.calAccountOrderTotalSpent(statusEnum.getValue());
    }

    @Override
    public List<DashboardChartCommonResp> selectTimezoneSaleStat(TimezoneSaleStatQuery query) {
        return this.baseMapper.selectTimezoneSaleStat(query.getStatTimes());
    }

    @Override
    public List<AdAccountOrderResp> selectCustomerUnClearOrderList(Long customerId) {
        return this.baseMapper.selectCustomerUnClearOrderList(customerId);
    }

    @Override
    public List<AdAccountOrderStatisticsResp> selectOrderStatistics(AdAccountOrderStatisticsQuery query) {
        List<AdAccountOrderStatisticsResp> list = this.baseMapper.sumOrderStatistics(query);

        if (CollUtil.isNotEmpty(list)) {
            list.forEach(row -> {
                row.setNormalRate(BigDecimal.ZERO);
                row.setSuspendedRate(BigDecimal.ZERO);
                row.setNoSpendRate(BigDecimal.ZERO);

                if (row.getNormalCount() > 0) {
                    row.setNormalRate(BigDecimal.valueOf(row.getNormalCount())
                        .divide(BigDecimal.valueOf(row.getTotalOrderCount()), 4, RoundingMode.HALF_UP)
                        .setScale(2, RoundingMode.HALF_UP));
                }
                if (row.getSuspendedCount() > 0) {
                    row.setSuspendedRate(BigDecimal.valueOf(row.getSuspendedCount())
                        .divide(BigDecimal.valueOf(row.getTotalOrderCount()), 4, RoundingMode.HALF_UP)
                        .setScale(2, RoundingMode.HALF_UP));
                }

                if (row.getNoSpendCount() > 0) {
                    row.setNoSpendRate(BigDecimal.valueOf(row.getNoSpendCount())
                        .divide(BigDecimal.valueOf(row.getTotalOrderCount()), 4, RoundingMode.HALF_UP)
                        .setScale(2, RoundingMode.HALF_UP));
                }
            });
        }

        return list;
    }

    @Override
    public void checkPrepayBalance() {
        List<AdAccountOrderDO> list = this.list(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getEnablePrepay, true)
            .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
        for (AdAccountOrderDO adAccountOrderDO : list) {
            AdAccountDO accountDO = adAccountService.getByPlatformAdId(adAccountOrderDO.getAdAccountId());
            if (accountDO.getAccountStatus().equals(AdAccountStatusEnum.BANNED)) {
                continue;
            }
            if (!adAccountOrderDO.getClearStatus().equals(AdAccountClearStatusEnum.WAIT)) {
                continue;
            }
            if (accountDO.getAmountSpent().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            BigDecimal lastPrepayBalance = adAccountHelper.getAdAccountLastPrepayBalance(adAccountOrderDO.getAdAccountId());
            if (lastPrepayBalance == null) {
                continue;
            }
            if (accountDO.getPrepayAccountBalance().compareTo(lastPrepayBalance) > 0) {
                continue;
            }
            BigDecimal recentSpent = lastPrepayBalance.subtract(accountDO.getPrepayAccountBalance());
            log.info("【可用充值金检测】广告户 {} 可用充值金剩余 {}，近10分钟消耗 {}", adAccountOrderDO.getAdAccountId(), NumberUtil.toStr(accountDO.getPrepayAccountBalance()), NumberUtil.toStr(recentSpent));
            if (accountDO.getPrepayAccountBalance().compareTo(new BigDecimal(20)) < 0) {
                BigDecimal rechargeAmount = NumberUtil.max(recentSpent.multiply(new BigDecimal(6)), new BigDecimal(30))
                    .subtract(accountDO.getPrepayAccountBalance())
                    .divide(BigDecimal.TEN, 0, RoundingMode.UP)
                    .multiply(BigDecimal.TEN);
                BigDecimal totalRecharge = customerBalanceRecordService.getTotalRechargeAmount(adAccountOrderDO.getCustomerId(), adAccountOrderDO.getAdAccountId(), adAccountOrderDO.getPayTime());
                BigDecimal spent = cardTransactionService.getAdAccountCardSpent(adAccountOrderDO.getCustomerId(), adAccountOrderDO.getAdAccountId(), adAccountOrderDO.getFinishTime());
                BigDecimal balance = totalRecharge.subtract(spent);
                CustomerDO customerDO = customerService.getById(adAccountOrderDO.getCustomerId());
                String msg;
                if (balance.compareTo(BigDecimal.ZERO) > 0) {
                    msg = "可用充值金即将用尽，建议充值$%s。".formatted(NumberUtil.toStr(NumberUtil.min(rechargeAmount, new BigDecimal(50), balance)));
                    if (balance.compareTo(BigDecimal.valueOf(100)) <= 0) {
                        msg += "请及时提醒客户补充余额。";
                    }
                } else {
                    msg = "账户余额已用完，请及时提醒客户补充余额。";
                }
                String text = BotUtils.createPrepayBalanceMessage(accountDO.getPlatformAdId(), customerDO.getName(), recentSpent, accountDO.getPrepayAccountBalance(), balance, msg);
                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getMonitorChatId())
                    .text(text)
                    .parseMode(ParseMode.MARKDOWNV2)
                    .build()));
            }
        }
    }

    @Override
    public void checkAdConfig() {
        List<String> ignoreList = List.of("***************", "****************");
        List<AdAccountOrderDO> orderList = this.baseMapper.selectCompleteAndNormalAdAccountOrderList();
        List<FbAdCampaignsDO> campaigns = fbAdCampaignsMapper.selectByDeliveryStatus("active");
        List<FbAdSetsDO> adSets = fbAdSetsMapper.selectList(Wrappers.emptyWrapper());
        for (AdAccountOrderDO accountOrderDO : orderList) {
            LocalDateTime startCampaignTime = null;
            boolean isOneDollar;
            // 检查开始广告系列时间
            List<FbAdCampaignsDO> existCampaigns = campaigns.stream()
                .filter(v -> v.getAdAccountId().equals(accountOrderDO.getAdAccountId()))
                .toList();
            if (!existCampaigns.isEmpty()) {
                for (FbAdCampaignsDO existCampaign : existCampaigns) {
                    if (existCampaign.getStartTime().isBefore(accountOrderDO.getPayTime())) {
                        // 部分系列日期读取有误
                        LocalDate date = LocalDateTimeUtil.parseDate("1970-01-01");
                        if (!LocalDateTimeUtil.isSameDay(date.atStartOfDay(), existCampaign.getStartTime())) {
                            log.info("【广告系列未关闭检测】广告户 {} 有历史广告系列未关闭，{}-{}", accountOrderDO.getAdAccountId(), existCampaign.getName(), LocalDateTimeUtil.formatNormal(existCampaign.getStartTime()));
                            if (!ignoreList.contains(accountOrderDO.getAdAccountId())) {
                                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                                    .chatId(telegramChatIdConfig.getMonitorChatId())
                                    .text("广告户 %s 存在未关闭的养户广告，名称 %s，开启时间 %s❌❌❌，请检查".formatted(accountOrderDO.getAdAccountId(), existCampaign.getName(), LocalDateTimeUtil.formatNormal(existCampaign.getStartTime())))
                                    .build()));
                            }
                        }
                    } else {
                        if (startCampaignTime == null || existCampaign.getStartTime().isBefore(startCampaignTime)) {
                            startCampaignTime = existCampaign.getStartTime();
                        }
                    }
                }
            }
            int oneDollarCount = 0;
            List<FbAdSetsDO> existAdSets = adSets.stream()
                .filter(v -> v.getAdAccountId().equals(accountOrderDO.getAdAccountId()))
                .toList();
            for (FbAdSetsDO adSetsDO : existAdSets) {
                Integer dailyBudget = adSetsDO.getDailyBudget();
                if (dailyBudget != null && BigDecimal.valueOf(dailyBudget).compareTo(new BigDecimal(200)) < 0) {
                    oneDollarCount++;
                }
            }
            isOneDollar = oneDollarCount >= 10;
            if (isOneDollar) {
                log.info("【一刀流检测】广告户 {} 识别到为一刀流投法", accountOrderDO.getAdAccountId());
            }
            this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
                .set(AdAccountOrderDO::getStartCampaignTime, startCampaignTime)
                .set(AdAccountOrderDO::getIsOneDollar, isOneDollar)
                // 如果第一次投放时间为空，且投放时间不为空，则更新第一次投放时间
                .set(null == accountOrderDO.getFirstStartCampaignTime() && null != startCampaignTime, AdAccountOrderDO::getFirstStartCampaignTime, startCampaignTime)
                .eq(AdAccountOrderDO::getId, accountOrderDO.getId()));
        }
    }

    @Override
    public void receive(AdAccountOrderReceiveReq req) {
        // 更新对应的下户订单接收状态
        lambdaUpdate().eq(AdAccountOrderDO::getId, req.getAdAccountOrderId())
            .eq(AdAccountOrderDO::getAdAccountId, req.getAdAccountId())
            .eq(AdAccountOrderDO::getCustomerId, req.getCustomerId())
            .set(AdAccountOrderDO::getTakeStatus, true)
            .update();
    }

    @Override
    public void resetClearStatus(Long id) {
        AdAccountOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(AdAccountOrderStatusEnum.AUTH_COMPLETED), "订单暂未授权完成");
        CheckUtils.throwIf(!order.getClearStatus().equals(AdAccountClearStatusEnum.CLEARED), "订单暂未清零");
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getClearStatus, AdAccountClearStatusEnum.WAIT)
            .set(AdAccountOrderDO::getClearTime, LocalDateTime.now())
            .eq(AdAccountOrderDO::getId, id));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.WAIT)
            .eq(AdAccountDO::getPlatformAdId, order.getAdAccountId()));
    }

    @Override
    public void freezeCard(Long customerId) {
        List<AdAccountOrderResp> list = this.selectCustomerUnClearOrderList(customerId);
        for (AdAccountOrderResp adAccountOrderResp : list) {
            adAccountService.freezeMasterCard(adAccountOrderResp.getAdAccountId());
        }
    }

    @Override
    public void unfreezeCard(Long customerId) {
        List<AdAccountOrderResp> list = this.selectCustomerUnClearOrderList(customerId);
        for (AdAccountOrderResp adAccountOrderResp : list) {
            adAccountService.unfreezeMasterCard(adAccountOrderResp.getAdAccountId());
        }
    }

    @Override
    public void updateAdAccountName(AdAccountOrderUpdateNameReq req) {
        this.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getAdAccountName, req.getAdAccountName())
            .eq(AdAccountOrderDO::getId, req.getId()));
    }
}