package top.continew.admin.biz.service.crm;

import top.continew.admin.biz.model.entity.SalesPersonnelMonthlyDataDO;
import top.continew.admin.biz.model.query.CustomerStatQuery;
import top.continew.admin.biz.model.query.PerformanceStatisticsQuery;
import top.continew.admin.biz.model.query.crm.SalesPerformanceStatQuery;
import top.continew.admin.biz.model.req.CustomerPerformanceInfoReq;
import top.continew.admin.biz.model.req.CustomerPerformanceOpportunityReq;
import top.continew.admin.biz.model.req.SalesPerformanceSubmitReq;
import top.continew.admin.biz.model.req.SalesPersonnelConfigReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;
import top.continew.admin.common.enums.DisEnableStatusEnum;
import top.continew.admin.system.enums.JobRankEnum;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;

/**
 * 商务业绩统计 Service
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface SalesPerformanceStatService {

    /**
     * 查询商务业绩统计
     *
     * @param query 查询条件
     * @return 商务业绩统计列表
     */
    List<SalesPerformanceStatResp> listSalesPerformanceStat(SalesPerformanceStatQuery query);

    List<SalesPersonnelMonthlyDataResp> listBusinessPerformanceStat(PerformanceStatisticsQuery query);

    SalesDataSummaryResp getSalesDataSummary(LocalDateTime startTime, LocalDateTime endTime, DisEnableStatusEnum userStatus);

    void submit(SalesPerformanceSubmitReq req);

    void submitConfig(SalesPersonnelConfigReq req);

    List<SalesPersonnelMonthlyDataDO> info(Long id, YearMonth yearMonth, JobRankEnum jobRank);

    List<CustomerPerformanceStatResp> customerStat(CustomerStatQuery query);

    List<CustomerPerformanceOpportunityResp> customerStatOpportunity(CustomerPerformanceOpportunityReq req);

    List<CustomerPerformanceInfoResp> customerStatCustomerInfo(CustomerPerformanceInfoReq req);

    List<CustomerRefundPerformanceStatResp> refundCustomerStat(CustomerStatQuery query);

    List<RefundCustomerStatInfoResp> refundCustomerStatInfo(CustomerPerformanceInfoReq req);
}