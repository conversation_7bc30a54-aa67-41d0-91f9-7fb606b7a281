/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.katai.strategy;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.poi.ss.formula.functions.T;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.katai.Client;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public interface CardOpsStrategy<T extends Client> {

    T getClient();

    /**
     * 所属卡台
     *
     * @return
     */
    CardPlatformEnum getCardPlatform();

    /**
     * 获取卡片列表
     *
     * @param start
     * @param end
     * @param syncPage 同步的页数
     * @return
     */
    List<CardDO> getCardList(LocalDateTime start, LocalDateTime end, Integer syncPage);

    /**
     * 获取卡号
     *
     * @param cardId
     * @return
     */
    CardDO getCardDetail(String cardId);

    /**
     * 获取卡的敏感信息，完整卡号，cvv、expiryDate
     *
     * @param cardId
     * @return
     */
    CardDO getCardSensitiveDetail(String cardId);

    /**
     * 获取卡片余额变更记录
     *
     * @param start
     * @param end
     * @return
     */
    List<CardBalanceDO> getCardBalanceList(LocalDateTime start, LocalDateTime end);

    /**
     * 获取卡片交易记录
     *
     * @param start
     * @param end
     * @param status
     * @return
     */
    List<CardTransactionDO> getCardTransactionList(LocalDateTime start, LocalDateTime end, String status);

    /**
     * 充值卡片
     *
     * @param cardDO
     * @param amount
     */
    void rechargeCard(CardDO cardDO, BigDecimal amount);

    /**
     * 提现卡片
     *
     * @param cardDO @return
     * @param amount
     */
    BigDecimal withdrawCard(CardDO cardDO, BigDecimal amount);

    /**
     * 开卡
     *
     * @param data@return
     */
    CardDO openCard(JSONObject data);

    /**
     * 获取卡片验证码
     *
     * @param card
     * @return
     */
    String getVerifyCode(CardDO card);

    /**
     * 更新备注
     *
     * @param card
     */
    void updateRemark(CardDO card);

    /**
     * 获取卡头
     *
     * @return
     */
    List<LabelValueResp<String>> getCardBinList();

    /**
     * 转换数据
     *
     * @param jsonArray
     * @return
     */
    List<CardTransactionDO> convertCardTransactionList(JSONArray jsonArray);

    /**
     * 激活卡片
     *
     * @param cardDO
     */
    void activeCard(CardDO cardDO);

    /**
     * 冻结卡片
     *
     * @param cardDO
     */
    void inactiveCard(CardDO cardDO);

    CardDO getApplyCardResult(String requestId);

    BigDecimal getCurrentBalance();

    /**
     * 获取卡片余额
     *
     * @param cardDO
     * @return
     */
    BigDecimal getCardBalance(CardDO cardDO);

}
