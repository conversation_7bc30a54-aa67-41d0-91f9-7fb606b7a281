/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;
import top.continew.starter.extension.crud.validation.CrudValidationGroup;

/**
 * 创建或修改下户订单参数
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
@Data
@Schema(description = "创建或修改下户订单参数")
public class AdAccountOrderReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @NotNull(message = "关联客户不能为空")
    private Long customerId;


    @NotBlank(message = "广告户ID不能为空")
    private String adAccountId;

    private String customerBmId;

    private String customerEmail;

    /**
     * 开户费
     */
    @Schema(description = "开户费")
    @NotNull(message = "开户费不能为空", groups = CrudValidationGroup.Add.class)
    private BigDecimal payAmount;

    @Schema(description = "广告户名字")
    private String adAccountName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;

    private Boolean enablePrepay;

    private Integer costParty;

    private Long businessUserId;
    private Integer orderMethod;

    private Long usedUserId;
}