package top.continew.admin.biz.model.query.crm;

import cn.hutool.core.date.DatePattern;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.common.enums.DisEnableStatusEnum;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商务业绩统计查询
 *
 * <AUTHOR>
 * @since 2025/01/16
 */
@Data
public class SalesPerformanceStatQuery extends PageQuery {

    /**
     * 商务人员ID列表
     */
    private List<Long> salesUserIds;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime endDate;

    private DisEnableStatusEnum userStatus;
}