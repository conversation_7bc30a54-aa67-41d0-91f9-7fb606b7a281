<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.crm.SalesPerformanceStatMapper">
    
    <select id="selectSalesPerformanceStat" resultType="top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp">
        SELECT DISTINCT
            u.id as salesUserId,
            u.nickname as salesUserName,
            
            -- 客咨数统计（从销售日报数据统计）
            IFNULL(sdd.total_customer_service, 0) as totalCustomerConsultations,
            IFNULL(sdd.wechat_customer_service, 0) as wechatConsultations,
            IFNULL(sdd.tg_customer_service, 0) as telegramConsultations,
            IFNULL(sdd.other_customer_service, 0) as otherConsultations,
            
            -- 线索数统计
            IFNULL(lead_stats.lead_total, 0) as totalLeads,
            IFNULL(lead_stats.lead_following, 0) as followingLeads,
            IFNULL(lead_stats.lead_opportunity, 0) as opportunityLeads,
            IFNULL(lead_stats.lead_invalid, 0) as invalidLeads,
            
            -- 商机数统计
            IFNULL(opp_stats.opportunity_total, 0) as totalOpportunities,
            IFNULL(opp_stats.opportunity_following, 0) as followingOpportunities,
            IFNULL(opp_stats.opportunity_won, 0) as wonOpportunities,
            IFNULL(opp_stats.opportunity_lost, 0) as lostOpportunities,

            -- 客户回访任务统计
            IFNULL(visit_stats.visit_total, 0) as totalVisits,
            IFNULL(visit_stats.visit_pending, 0) as pendingVisits,
            IFNULL(visit_stats.visit_processing, 0) as processingVisits,
            IFNULL(visit_stats.visit_completed, 0) as completedVisits,
            -- 成交客户数
            IFNULL(customer_stats.deal_customer_count, 0) as formalCustomers
            
        FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.id = ur.user_id
        LEFT JOIN sys_role r ON r.id = ur.role_id
        
        -- 客咨数统计（从销售日报数据统计，每条记录代表一个客咨）
        LEFT JOIN (
            SELECT 
                create_user,
                COUNT(*) as total_customer_service,
                SUM(CASE WHEN account_type = 1 THEN 1 ELSE 0 END) as wechat_customer_service,
                SUM(CASE WHEN account_type = 2 THEN 1 ELSE 0 END) as tg_customer_service,
                SUM(CASE WHEN account_type NOT IN (1, 2) THEN 1 ELSE 0 END) as other_customer_service
            FROM biz_sales_daily_data 
            WHERE record_date BETWEEN #{startDate} AND #{endDate}
            GROUP BY create_user
        ) sdd ON u.id = sdd.create_user
        
        -- 线索数统计
        LEFT JOIN (
            SELECT 
                handler_user_id,
                COUNT(*) as lead_total,
                SUM(CASE WHEN status IN (2, 3) THEN 1 ELSE 0 END) as lead_following,
                SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as lead_opportunity,
                SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as lead_invalid
            FROM biz_lead 
            WHERE create_time BETWEEN #{startDate} AND #{endDate}
            GROUP BY handler_user_id
        ) lead_stats ON u.id = lead_stats.handler_user_id
        
        -- 商机数统计
        LEFT JOIN (
            SELECT 
                handler_user_id,
                COUNT(*) as opportunity_total,
                SUM(CASE WHEN status IN (2, 3) THEN 1 ELSE 0 END) as opportunity_following,
                SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as opportunity_won,
                SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as opportunity_lost
            FROM biz_opportunity 
            WHERE create_time BETWEEN #{startDate} AND #{endDate}
            GROUP BY handler_user_id
        ) opp_stats ON u.id = opp_stats.handler_user_id
        
        -- 成交客户数统计
        LEFT JOIN (
            SELECT
            business_user_id,
                COUNT(*) as deal_customer_count
            FROM biz_customer 
            WHERE type = 1
            AND status = 1
            AND create_time BETWEEN #{startDate} AND #{endDate}
            GROUP BY business_user_id
        ) customer_stats ON u.id = customer_stats.business_user_id

        -- 客户回访任务统计
        LEFT JOIN (
        SELECT
        assignee_id, -- 按负责人ID分组
        COUNT(*) as visit_total, -- 统计总任务数
        SUM(CASE WHEN task_status = 1 THEN 1 ELSE 0 END) as visit_pending,    -- 统计待处理任务
        SUM(CASE WHEN task_status = 2 THEN 1 ELSE 0 END) as visit_processing, -- 统计处理中任务
        SUM(CASE WHEN task_status = 3 THEN 1 ELSE 0 END) as visit_completed   -- 统计已完成任务
        FROM biz_customer_visit_task
        WHERE create_time BETWEEN #{startDate} AND #{endDate} -- 应用统一的时间范围筛选
        GROUP BY assignee_id -- 按负责人进行聚合
        ) visit_stats ON u.id = visit_stats.assignee_id -- 将聚合结果与用户表关联
        WHERE (r.code LIKE 'business_%' or r.code='admin')
        <if test="userStatus != null">
            AND u.status = #{userStatus}
        </if>
        <if test="salesUserIds != null and salesUserIds.size() > 0">
            AND u.id IN 
            <foreach collection="salesUserIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        
        ORDER BY IFNULL(sdd.total_customer_service, 0) DESC, u.id
    </select>
    <select id="selectProbationStat"
            resultType="top.continew.admin.biz.model.resp.BusinessPerformanceStatisticsResp">
        WITH dailySummary AS (SELECT su.id          AS userId,
                                     COUNT(bsds.id) AS dailyReportCount
                              FROM biz_sales_daily_summary bsds
                                       JOIN sys_user su ON su.id = bsds.create_user
                              WHERE bsds.record_date >= CAST(su.create_time AS DATE)
                                AND bsds.record_date &lt;= DATE_ADD(CAST(su.create_time AS DATE)
                                  , INTERVAL 6 DAY)
                              GROUP BY su.id),
             dailyData AS (SELECT userId,
                                  COUNT(DISTINCT daily_date) AS data
                           FROM (SELECT su.id            AS userId,
                                        bsdd.record_date AS daily_date
                                 FROM biz_sales_daily_data bsdd
                                          JOIN sys_user su ON bsdd.create_user = su.id
                                 WHERE bsdd.record_date >= CAST(su.create_time AS DATE)
                                   AND bsdd.record_date &lt;= DATE_ADD(CAST(su.create_time AS DATE), INTERVAL 6 DAY)
                                 GROUP BY su.id, bsdd.record_date) AS subquery_result
                           GROUP BY userId),
             bizDataSummary AS (SELECT bsdd.create_user                     AS userId,
                                       SUM(IF(bsdd.account_type = 2, 1, 0)) AS wechatFriendsCount,
                                       SUM(IF(bsdd.account_type = 1, 1, 0)) AS telegramFriendsCount
                                FROM biz_sales_daily_data bsdd
                                         JOIN sys_user su ON su.id = bsdd.create_user
                                WHERE bsdd.record_date >= CAST(su.create_time AS DATE)
                                  AND bsdd.record_date &lt;= DATE_ADD(CAST(su.create_time AS DATE)
                                    , INTERVAL 6 DAY)
                                GROUP BY bsdd.create_user)
        SELECT su.nickname                               AS businessName,
               su.id as businessUserId,
               su.create_time                            AS entryTime,
               COALESCE(bds.wechatFriendsCount, 0)       AS wechatFriendsCount,
               COALESCE(bds.telegramFriendsCount, 0)     AS telegramFriendsCount,
               ABS(COALESCE(ds.dailyReportCount, 0) - 6) AS dailyReportMissingCount,
               ABS(COALESCE(dd.data, 0) - 6)             AS customerConsultingMissingCount
        FROM sys_user su
                 LEFT JOIN bizDataSummary bds ON bds.userId = su.id
                 LEFT JOIN dailySummary ds ON ds.userId = su.id
                 LEFT JOIN dailyData dd ON dd.userId = su.id
        WHERE su.id IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
    <select id="selectTrialStat"
            resultType="top.continew.admin.biz.model.resp.BusinessPerformanceStatisticsResp">
        WITH ClosedCustomers AS (SELECT bc.business_user_id AS userId,
                                        COUNT(bc.id)        AS closedCustomersCount
                                 FROM biz_customer bc
                                 WHERE bc.`type` = 1 AND bc.status=1
                                   AND bc.create_time >= #{startDate}
                                   AND bc.create_time &lt;= #{endDate}
                                 GROUP BY bc.business_user_id),
             Friends AS (SELECT bsdd.create_user as userId,
                                count(bsdd.id)         AS friendsCount
                         FROM biz_sales_daily_data bsdd
                         WHERE bsdd.record_date >= #{startDate}
                           AND bsdd.record_date &lt;= #{endDate}
                         GROUP BY bsdd.create_user),
             IntendedCustomers AS (SELECT bo.create_user AS userId,
                                          COUNT(*)       AS intendedCustomersCount
                                   FROM biz_opportunity bo
                                   WHERE bo.`status` IN (2, 3, 4)
                                     AND bo.create_time >= #{startDate}
                                     AND bo.create_time &lt;= #{endDate}
                                   GROUP BY bo.create_user),
             CustomerSpend AS (SELECT baai.business_user_id          AS userId,
                                      COALESCE(SUM(baai.spend), 0) AS totalCustomerSpend
                               FROM biz_ad_account_insight baai
                               WHERE baai.stat_date >= #{startDate}
                                 AND baai.stat_date &lt;= #{endDate}
                               GROUP BY baai.business_user_id),
             dailySummary AS (SELECT su.id          AS userId,
                                     COUNT(bsds.id) AS dailyReportCount
                              FROM biz_sales_daily_summary bsds
                                       JOIN sys_user su ON su.id = bsds.create_user
                              WHERE bsds.record_date >= #{startDate}
                                AND bsds.record_date &lt;= #{endDate}
                              GROUP BY su.id),
             dailyData AS (SELECT userId,
                                  COUNT(DISTINCT daily_date) AS data
                           FROM (SELECT su.id                  AS userId,
                                        bsdd.record_date AS daily_date
                                 FROM biz_sales_daily_data bsdd
                                          JOIN sys_user su ON bsdd.create_user = su.id
                                 WHERE bsdd.record_date >= #{startDate}
                                   AND bsdd.record_date &lt;= #{endDate}
                                 GROUP BY su.id, bsdd.record_date) AS subquery_result
                           GROUP BY userId)
        select su.nickname                                            AS businessName,
                su.id as businessUserId,
               COALESCE(ClosedCustomers.closedCustomersCount, 0)      AS closedCustomersCount,
               COALESCE(Friends.friendsCount, 0)                      AS friendsCount,
               COALESCE(IntendedCustomers.intendedCustomersCount, 0)  AS intendedCustomersCount,
               COALESCE(CustomerSpend.totalCustomerSpend, 0)          AS totalCustomerSpend,
               ABS(COALESCE(ds.dailyReportCount, 0) - #{workingDays}) AS dailyReportMissingCount,
               ABS(COALESCE(dd.data, 0) - #{workingDays})             AS customerConsultingMissingCount
        from sys_user su
                 left join ClosedCustomers on ClosedCustomers.userId = su.id
                 left join Friends on Friends.userId = su.id
                 left join IntendedCustomers on IntendedCustomers.userId = su.id
                 left join CustomerSpend on CustomerSpend.userId = su.id
                 LEFT JOIN dailySummary ds ON ds.userId = su.id
                 LEFT JOIN dailyData dd ON dd.userId = su.id
        where su.id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
    <select id="selectFormalStat"
            resultType="top.continew.admin.biz.model.resp.BusinessPerformanceStatisticsResp">
        WITH dailySummary AS (SELECT su.id          AS userId,
                                     COUNT(bsds.id) AS dailyReportCount
                              FROM biz_sales_daily_summary bsds
                                       JOIN sys_user su ON su.id = bsds.create_user
                              WHERE bsds.record_date >= #{startDate}
                                AND bsds.record_date &lt;= #{endDate}
                              GROUP BY su.id),
             dailyData AS (SELECT userId,
                                  COUNT(DISTINCT daily_date) AS data
                           FROM (SELECT su.id            AS userId,
                                        bsdd.record_date AS daily_date
                                 FROM biz_sales_daily_data bsdd
                                          JOIN sys_user su ON bsdd.create_user = su.id
                                 WHERE bsdd.record_date >= #{startDate}
                                   AND bsdd.record_date &lt;= #{endDate}
                                 GROUP BY su.id, bsdd.record_date) AS subquery_result
                           GROUP BY userId),
             ClosedCustomers AS (SELECT bc.business_user_id AS userId,
                                        COUNT(bc.id)        AS closedCustomersCount
                                 FROM biz_customer bc
                                 WHERE bc.`type` = 1
                                   AND bc.status = 1
                                   AND bc.create_time >= #{startDate}
                                   AND bc.create_time &lt;= #{endDate}
                                 GROUP BY bc.business_user_id),
             Friends AS (SELECT bsdd.create_user as userId,
                                count(*)         AS friendsCount
                         FROM biz_sales_daily_data bsdd
                         WHERE bsdd.create_time >= #{startDate}
                           AND bsdd.create_time &lt;= #{endDate}
                         GROUP BY bsdd.create_user),
             CustomerSpend AS (SELECT baai.business_user_id        AS userId,
                                      COALESCE(SUM(baai.spend), 0) AS customerSpend
                               FROM biz_ad_account_insight baai
                               WHERE baai.stat_date >= #{startDate}
                                 AND baai.stat_date &lt;= #{endDate}
                               GROUP BY baai.business_user_id),
             adAccountOrder AS (SELECT baao.business_user_id                                                  AS userId,
                                       COUNT(baao.id)                                                         AS totalAdAccount,
                                       COUNT(CASE WHEN baao.start_campaign_time IS NOT NULL THEN baao.id END) AS totalNormalAdAccount
                                FROM biz_ad_account_order baao
                                         JOIN biz_ad_account baa ON baa.platform_ad_id = baao.ad_account_id
                                WHERE baao.`status` = 3
                                  AND baa.account_status = 1
                                GROUP BY baao.business_user_id),
             CustomerTotalSpend AS (SELECT ai.business_user_id                                 AS userId,
                                           COALESCE(SUM(ai.spend), 0)                          AS totalSpend,
                                           (SELECT COUNT(DISTINCT baao.ad_account_id)
                                            FROM biz_ad_account_order baao
                                            WHERE baao.`status` = 3
                                              AND baao.business_user_id = ai.business_user_id) AS totalAdAccountCount
                                    FROM biz_ad_account_insight ai
                                    GROUP BY ai.business_user_id),
             customerInfo AS (SELECT bc.business_user_id AS userId,
                                     COUNT(bc.id)        AS totalCustomerCount,
                                     COUNT(bcwo.id)      AS totalChurnCount
                              FROM biz_customer bc
                                       LEFT JOIN biz_customer_withdraw_order bcwo ON bc.id = bcwo.customer_id
                                  AND bcwo.`status` = 4
                              WHERE bc.type = 1
                              GROUP BY bc.business_user_id)
        SELECT su.nickname                                            AS businessName,
               su.id                                                  AS businessUserId,
               COALESCE(ClosedCustomers.closedCustomersCount, 0)      AS closedCustomersCount,
               COALESCE(Friends.friendsCount, 0)                      AS friendsCount,
               ROUND(
                       IF(adAccountOrder.totalAdAccount > 0,
                          CAST(adAccountOrder.totalNormalAdAccount AS DECIMAL(10, 2)) / adAccountOrder.totalAdAccount *
                          100, 0), 2
               )                                                      AS adAccountUsageRate,
               COALESCE(CustomerSpend.customerSpend, 0)               AS totalCustomerSpend,
               ROUND(
                       IF(customerInfo.totalCustomerCount > 0,
                          (CAST(customerInfo.totalCustomerCount AS DECIMAL(10, 2)) -
                           COALESCE(customerInfo.totalChurnCount, 0)) / customerInfo.totalCustomerCount * 100
                           , 0
                       ), 2
               )                                                      AS customerRetentionRate,
               ROUND(
                       IF(CustomerTotalSpend.totalAdAccountCount > 0,
                          CAST(CustomerTotalSpend.totalSpend AS DECIMAL(10, 2)) /
                          CustomerTotalSpend.totalAdAccountCount, 0), 2
               )                                                      AS avgSingleAccountSpend,
               ABS(COALESCE(ds.dailyReportCount, 0) - #{workingDays}) AS dailyReportMissingCount,
               ABS(COALESCE(dd.data, 0) - #{workingDays})             AS customerConsultingMissingCount
        FROM sys_user su
                 LEFT JOIN ClosedCustomers ON ClosedCustomers.userId = su.id
                 LEFT JOIN Friends ON Friends.userId = su.id
                 LEFT JOIN CustomerSpend ON CustomerSpend.userId = su.id
                 LEFT JOIN dailySummary ds ON ds.userId = su.id
                 LEFT JOIN dailyData dd ON dd.userId = su.id
                 LEFT JOIN adAccountOrder ON adAccountOrder.userId = su.id
                 LEFT JOIN customerInfo ON customerInfo.userId = su.id
                 LEFT JOIN CustomerTotalSpend ON CustomerTotalSpend.userId = su.id
        WHERE su.id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectSalesDataSummary" resultType="top.continew.admin.biz.model.resp.SalesDataSummaryResp">
        WITH UserIDs AS (SELECT DISTINCT su.id AS user_id
                         FROM sys_user su
                                  LEFT JOIN
                              sys_user_role ur ON su.id = ur.user_id
                                  LEFT JOIN
                              sys_role r ON r.id = ur.role_id
                         WHERE (r.CODE LIKE 'business_%' OR r.CODE = 'admin')
                            <if test="userStatus != null">
                                AND su.STATUS = #{userStatus}
                            </if>),
             CustomerCounts AS (SELECT COUNT(bc.id) AS total_deals_customers
                                FROM biz_customer bc
                                         INNER JOIN
                                     UserIDs ui ON bc.business_user_id = ui.user_id
                                WHERE bc.`type` = 1
                                  AND bc.STATUS = 1
                                  AND bc.create_time between #{startDate} AND #{endDate}),
             FriendCounts AS (SELECT COUNT(bsdd.id) AS total_new_friends
                              FROM biz_sales_daily_data bsdd
                                       INNER JOIN
                                   UserIDs ui ON bsdd.create_user = ui.user_id
                              WHERE bsdd.record_date between #{startDate} AND #{endDate})
        SELECT cc.total_deals_customers as totalDealsCustomers,
               fc.total_new_friends as totalNewFriends
        FROM CustomerCounts cc,
             FriendCounts fc
    </select>
    <select id="selectCustomerStatBase"
            resultType="top.continew.admin.biz.model.resp.CustomerPerformanceStatResp">
        WITH ValidOpportunities AS (-- 有效拉群数
            SELECT bo.handler_user_id AS businessUserId,
                   COUNT(bo.id)       AS validOpportunityCount
            FROM biz_opportunity bo
            WHERE bo.status IN (2, 3, 4)
              AND bo.create_time BETWEEN #{startTime} AND #{endTime}
            GROUP BY bo.handler_user_id),
             FirstChargeNewCustomers AS (-- 首充新客户数
                 SELECT bc.business_user_id AS businessUserId,
                        COUNT(bc.id)        AS firstChargeCustomerCount
                 FROM biz_customer bc
                 WHERE bc.type = 1
                   AND bc.status = 1
                   AND bc.cooperate_time BETWEEN #{startTime} AND #{endTime}
                 GROUP BY bc.business_user_id)
        SELECT su.nickname                           as businessUserName,
               su.id                                 as businessUserId,
               IFNULL(v.validOpportunityCount, 0)    as validGroupCount,
               IFNULL(f.firstChargeCustomerCount, 0) as firstRechargeNewCustomerCount
        FROM sys_user su
                 LEFT JOIN ValidOpportunities v ON v.businessUserId = su.id
                 LEFT JOIN FirstChargeNewCustomers f ON f.businessUserId = su.id
        WHERE su.dept_id = 702164640200656226
        <if test="userStatus != null">
            AND su.status = #{userStatus}
        </if>
        <if test="businessUserIds != null and businessUserIds.size() > 0">
            AND su.id IN
            <foreach collection="businessUserIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectCustomerFirstMonthConsumption" resultType="java.util.Map">
        WITH CustomerBase AS (-- 所有在统计月份内合作的正式且正常状态的客户
            SELECT bc.id                                        AS customer_id,
                   bc.business_user_id                          AS businessUserId,
                   bc.cooperate_time,
                   DATE_ADD(bc.cooperate_time, INTERVAL 30 DAY) AS endTime
            FROM biz_customer bc
            WHERE bc.type = 1
              AND bc.`status` = 1
              AND bc.cooperate_time BETWEEN #{startTime} AND #{endTime}
              AND bc.business_user_id = #{businessUserId}),
             FirstMonthSpent AS (-- 聚合每个客户在其首月内的总消耗
                 SELECT bct.customer_id,
                        SUM(bct.trans_amount) AS total_consumption
                 FROM biz_card_transaction bct
                          JOIN CustomerBase cb ON bct.customer_id = cb.customer_id
                     AND bct.stat_time BETWEEN cb.cooperate_time
                                                      AND cb.endTime
                     AND bct.business_user_id = cb.businessUserId
                 WHERE bct.trans_status != 4
                 GROUP BY bct.customer_id),
             FirstMonthAccounts AS (SELECT baao.customer_id,
                                           COUNT(DISTINCT baao.ad_account_id) AS total_accounts
                                    FROM biz_ad_account_order baao
                                             JOIN CustomerBase cb ON baao.customer_id = cb.customer_id
                                        AND baao.finish_time BETWEEN cb.cooperate_time
                                                                         AND cb.endTime
                                    WHERE baao.`status` IN (3, 5)
                                    GROUP BY baao.customer_id),
             FirstMonthCalculatedData AS (-- 合并首月消耗和户数，并计算平均消耗
                 SELECT cb.customer_id,
                        cb.businessUserId,
                        COALESCE(fmt.total_consumption, 0)       AS first_month_total_consumption,
                        COALESCE(fma.total_accounts, 0)          AS first_month_total_accounts,
                        IF
                        (
                                COALESCE(fma.total_accounts, 0) = 0,
                                0,
                                COALESCE(fmt.total_consumption, 0) /
                                COALESCE(fma.total_accounts, 0)) AS average_consumption
                 FROM CustomerBase cb
                          LEFT JOIN FirstMonthSpent fmt ON cb.customer_id = fmt.customer_id
                          LEFT JOIN FirstMonthAccounts fma ON cb.customer_id = fma.customer_id)
        SELECT
            COALESCE(SUM(IF(average_consumption > 3000 AND average_consumption &lt;= 10000, 1, 0)), 0)  AS '1Count',
            COALESCE(SUM(IF(average_consumption > 10000 AND average_consumption &lt;= 30000, 1, 0)), 0) AS '2Count',
            COALESCE(SUM(IF(average_consumption > 30000 AND average_consumption &lt;= 50000, 1, 0)), 0) AS '3Count'
        FROM FirstMonthCalculatedData
        WHERE average_consumption > 3000
    </select>
    <select id="selectCustomerStatOpportunity"
            resultType="top.continew.admin.biz.model.resp.CustomerPerformanceOpportunityResp">
        SELECT bc.name as customerName,
               bc.type,
               bc.status,
               bc.telegram_chat_id,
               bc.create_time
        FROM biz_opportunity bo
                 LEFT JOIN biz_customer bc ON bo.customer_id = bc.id
        WHERE bo.STATUS IN (2, 3, 4)
          AND bo.create_time BETWEEN #{startTime} AND #{endTime}
          AND bo.handler_user_id = #{businessUserId}
    </select>
    <select id="selectCustomerStatFirstRechargeNewCustomer"
            resultType="top.continew.admin.biz.model.resp.CustomerPerformanceInfoResp">
        SELECT bc.name AS customerName,
               bc.type,
               bc.status,
               bc.cooperate_time
        FROM biz_customer bc
        WHERE bc.type = 1
          AND bc.STATUS = 1
          AND bc.cooperate_time BETWEEN #{startTime} AND #{endTime}
          AND bc.business_user_id = #{businessUserId}
    </select>
    <select id="selectCustomerFirstMonthConsumptionQualifiedNewCustomer"
            resultType="top.continew.admin.biz.model.resp.CustomerPerformanceInfoResp">
        WITH CustomerBase AS (-- 所有在统计月份内合作的正式且正常状态的客户
            SELECT bc.id                                        AS customer_id,
                   bc.business_user_id                          AS businessUserId,
                   bc.cooperate_time,
                   DATE_ADD(bc.cooperate_time, INTERVAL 30 DAY) AS endTime
            FROM biz_customer bc
            WHERE bc.type = 1
              AND bc.`status` = 1
              AND bc.business_type = 1
              AND bc.cooperate_time BETWEEN #{startTime} AND #{endTime}
              AND bc.business_user_id = #{businessUserId}),
             FirstMonthSpent AS (-- 聚合每个客户在其首月内的总消耗
                 SELECT bct.customer_id,
                        SUM(bct.trans_amount) AS total_consumption
                 FROM biz_card_transaction bct
                          JOIN CustomerBase cb ON bct.customer_id = cb.customer_id
                     AND bct.stat_time BETWEEN cb.cooperate_time
                                                      AND cb.endTime
                     AND bct.business_user_id = cb.businessUserId
                 WHERE bct.trans_status != 4
                 GROUP BY bct.customer_id),
             FirstMonthAccounts AS (SELECT baao.customer_id,
                                           COUNT(DISTINCT baao.ad_account_id) AS total_accounts
                                    FROM biz_ad_account_order baao
                                             JOIN CustomerBase cb ON baao.customer_id = cb.customer_id
                                        AND baao.finish_time BETWEEN cb.cooperate_time
                                                                         AND cb.endTime
                                    WHERE baao.`status` IN (3, 5)
                                    GROUP BY baao.customer_id),
             FirstMonthCalculatedData AS (-- 合并首月消耗和户数，并计算平均消耗
                 SELECT cb.customer_id,
                        cb.businessUserId,
                        COALESCE(fmt.total_consumption, 0) AS first_month_total_consumption,
                        COALESCE(fma.total_accounts, 0)    AS first_month_total_accounts,
                        IF
                        (COALESCE(fma.total_accounts, 0) = 0,
                         0,
                         COALESCE(fmt.total_consumption, 0) /
                         COALESCE(fma.total_accounts, 0))  AS average_consumption
                 FROM CustomerBase cb
                          LEFT JOIN FirstMonthSpent fmt ON cb.customer_id = fmt.customer_id
                          LEFT JOIN FirstMonthAccounts fma ON cb.customer_id = fma.customer_id)
        SELECT bc.name AS customerName,
               bc.type,
               bc.status,
               bc.cooperate_time
        from FirstMonthCalculatedData
                 left join biz_customer bc on bc.id = FirstMonthCalculatedData.customer_id
        <where>
            <if test="type == 2">
                average_consumption > 3000 AND average_consumption &lt;= 10000
            </if>
            <if test="type == 3">
                average_consumption > 10000 AND average_consumption &lt;= 30000
            </if>
            <if test="type == 4">
                average_consumption > 30000 AND average_consumption &lt;= 50000
            </if>
        </where>
    </select>
    <select id="selectRefundCustomerStatBase"
            resultType="top.continew.admin.biz.model.resp.CustomerRefundPerformanceStatResp">
        SELECT bc.business_user_id AS businessUserId,
               COUNT(bc.id)        AS activatedCustomer
        FROM biz_customer bc
        WHERE bc.type = 1
          AND bc.STATUS = 1
          AND bc.business_type = 1
          AND bc.last_cooperate_time > bc.cooperate_time
          AND bc.last_cooperate_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY bc.business_user_id
    </select>
    <select id="selectRefundCustomerFirstMonthConsumption" resultType="java.util.Map">
        WITH CustomerBase AS (-- 所有在统计月份内合作的正式且正常状态的客户
            SELECT bc.id                                             AS customer_id,
                   bc.business_user_id                               AS businessUserId,
                   bc.last_cooperate_time,
                   DATE_ADD(bc.last_cooperate_time, INTERVAL 30 DAY) AS endTime
            FROM biz_customer bc
            WHERE bc.type = 1
              AND bc.`status` = 1
              AND bc.last_cooperate_time BETWEEN #{startTime} AND #{endTime}
              AND bc.last_cooperate_time > bc.cooperate_time
              AND bc.business_user_id = #{businessUserId}),
             FirstMonthSpent AS (-- 聚合每个客户在其首月内的总消耗
                 SELECT bct.customer_id,
                        SUM(bct.trans_amount) AS total_consumption
                 FROM biz_card_transaction bct
                          JOIN CustomerBase cb ON bct.customer_id = cb.customer_id
                     AND bct.stat_time BETWEEN cb.last_cooperate_time
                                                      AND cb.endTime
                     AND bct.business_user_id = cb.businessUserId
                 WHERE bct.trans_status != 4
                 GROUP BY bct.customer_id),
             FirstMonthAccounts AS (SELECT baao.customer_id,
                                           COUNT(DISTINCT baao.ad_account_id) AS total_accounts
                                    FROM biz_ad_account_order baao
                                             JOIN CustomerBase cb ON baao.customer_id = cb.customer_id
                                        AND baao.finish_time BETWEEN cb.last_cooperate_time
                                                                         AND cb.endTime
                                    WHERE baao.`status` IN (3, 5)
                                    GROUP BY baao.customer_id),
             FirstMonthCalculatedData AS (-- 合并首月消耗和户数，并计算平均消耗
                 SELECT cb.customer_id,
                        cb.businessUserId,
                        COALESCE(fmt.total_consumption, 0)       AS first_month_total_consumption,
                        COALESCE(fma.total_accounts, 0)          AS first_month_total_accounts,
                        IF
                        (
                                COALESCE(fma.total_accounts, 0) = 0,
                                0,
                                COALESCE(fmt.total_consumption, 0) /
                                COALESCE(fma.total_accounts, 0)) AS average_consumption
                 FROM CustomerBase cb
                          LEFT JOIN FirstMonthSpent fmt ON cb.customer_id = fmt.customer_id
                          LEFT JOIN FirstMonthAccounts fma ON cb.customer_id = fma.customer_id)
        SELECT COALESCE(SUM(IF(average_consumption > 3000 AND average_consumption &lt;= 10000, 1, 0)), 0)  AS '1Count',
               COALESCE(SUM(IF(average_consumption > 10000 AND average_consumption &lt;= 30000, 1, 0)), 0) AS '2Count',
               COALESCE(SUM(IF(average_consumption > 30000 AND average_consumption &lt;= 50000, 1, 0)), 0) AS '3Count'
        FROM FirstMonthCalculatedData
        WHERE average_consumption > 3000
    </select>
    <select id="selectRefundCustomerStatInfo"
            resultType="top.continew.admin.biz.model.resp.RefundCustomerStatInfoResp">
        SELECT bc.name AS customerName,
               bc.type,
               bc.status,
               bc.cooperate_time,
               bc.last_cooperate_time
        FROM biz_customer bc
        WHERE bc.type = 1
          AND bc.STATUS = 1
          AND bc.last_cooperate_time > bc.cooperate_time
          AND bc.last_cooperate_time BETWEEN #{startTime} AND #{endTime}
          AND bc.business_user_id = #{businessUserId}
    </select>
    <select id="selectRefundCustomerFirstMonth"
            resultType="top.continew.admin.biz.model.resp.RefundCustomerStatInfoResp">
        WITH CustomerBase AS (-- 所有在统计月份内合作的正式且正常状态的客户
            SELECT bc.id                                             AS customer_id,
                   bc.business_user_id                               AS businessUserId,
                   bc.last_cooperate_time,
                   DATE_ADD(bc.last_cooperate_time, INTERVAL 30 DAY) AS endTime
            FROM biz_customer bc
            WHERE bc.type = 1
              AND bc.`status` = 1
              AND bc.last_cooperate_time BETWEEN #{startTime} AND #{endTime}
              AND bc.last_cooperate_time > bc.cooperate_time
              AND bc.business_user_id = #{businessUserId}),
             FirstMonthSpent AS (-- 聚合每个客户在其首月内的总消耗
                 SELECT bct.customer_id,
                        SUM(bct.trans_amount) AS total_consumption
                 FROM biz_card_transaction bct
                          JOIN CustomerBase cb ON bct.customer_id = cb.customer_id
                     AND bct.stat_time BETWEEN cb.last_cooperate_time
                                                      AND cb.endTime
                     AND bct.business_user_id = cb.businessUserId
                 WHERE bct.trans_status != 4
                 GROUP BY bct.customer_id),
             FirstMonthAccounts AS (SELECT baao.customer_id,
                                           COUNT(DISTINCT baao.ad_account_id) AS total_accounts
                                    FROM biz_ad_account_order baao
                                             JOIN CustomerBase cb ON baao.customer_id = cb.customer_id
                                        AND baao.finish_time BETWEEN cb.last_cooperate_time
                                                                         AND cb.endTime
                                    WHERE baao.`status` IN (3, 5)
                                    GROUP BY baao.customer_id),
             FirstMonthCalculatedData AS (-- 合并首月消耗和户数，并计算平均消耗
                 SELECT cb.customer_id,
                        cb.businessUserId,
                        COALESCE(fmt.total_consumption, 0) AS first_month_total_consumption,
                        COALESCE(fma.total_accounts, 0)    AS first_month_total_accounts,
                        IF
                        (COALESCE(fma.total_accounts, 0) = 0,
                         0,
                         COALESCE(fmt.total_consumption, 0) /
                         COALESCE(fma.total_accounts, 0))  AS average_consumption
                 FROM CustomerBase cb
                          LEFT JOIN FirstMonthSpent fmt ON cb.customer_id = fmt.customer_id
                          LEFT JOIN FirstMonthAccounts fma ON cb.customer_id = fma.customer_id)
        SELECT bc.name AS customerName,
               bc.type,
               bc.status,
               bc.cooperate_time
        from FirstMonthCalculatedData
                 left join biz_customer bc on bc.id = FirstMonthCalculatedData.customer_id
        <where>
            <if test="type == 2">
                average_consumption > 3000 AND average_consumption &lt;= 10000
            </if>
            <if test="type == 3">
                average_consumption > 10000 AND average_consumption &lt;= 30000
            </if>
            <if test="type == 4">
                average_consumption > 30000 AND average_consumption &lt;= 50000
            </if>
        </where>
    </select>


</mapper>