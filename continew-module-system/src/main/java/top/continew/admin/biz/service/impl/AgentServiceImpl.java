package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.admin.biz.mapper.AgentMapper;
import top.continew.admin.biz.model.entity.AgentDO;
import top.continew.admin.biz.model.query.AgentQuery;
import top.continew.admin.biz.model.req.AgentReq;
import top.continew.admin.biz.model.resp.AgentDetailResp;
import top.continew.admin.biz.model.resp.AgentResp;
import top.continew.admin.biz.service.AgentService;
import top.continew.admin.biz.service.TransactionUserService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

/**
 * 中介业务实现
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Service
@RequiredArgsConstructor
public class AgentServiceImpl extends BaseServiceImpl<<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>il<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eq> implements AgentService {

    private final TransactionUserService transactionUserService;

    @Override
    protected void afterAdd(AgentReq req, AgentDO entity) {
        transactionUserService.getUser(entity.getId(), TransactionUserTypeEnum.AD_ACCOUNT_AGENT, entity.getName());
    }
}