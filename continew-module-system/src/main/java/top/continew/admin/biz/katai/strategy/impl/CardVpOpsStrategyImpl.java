/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.katai.strategy.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.katai.CardVpClient;
import top.continew.katai.CardVpCrawlerClient;
import top.continew.katai.CommonResponse;
import top.continew.katai.cardvp.CardVpConfig;
import top.continew.katai.cardvp.CardVpCrawlerConfig;
import top.continew.katai.cardvp.model.CVPaginationRes;
import top.continew.katai.cardvp.model.auth.CVTransfer;
import top.continew.katai.cardvp.model.card.CVCard;
import top.continew.katai.cardvp.model.card.CVCreateCardRequest;
import top.continew.katai.cardvp.model.card.CVUpdateCardAmountRequest;
import top.continew.katai.cardvp.model.card.CVUpdateCardInformationRequest;
import top.continew.katai.cardvp.model.transaction.CVGetTransactionsRequest;
import top.continew.katai.cardvp.model.transaction.CVTransaction;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.utils.Common;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CardVpOpsStrategyImpl implements CardOpsStrategy<CardVpClient> {

    public static final int DEFAULT_PAGE_SIZE = 100;

    private final CardVpCrawlerClient cardVpCrawlerClient = new CardVpCrawlerClient(new CardVpCrawlerConfig().setEndpoint("www.carhomy.com")
        .setUsername("xiaozhuang")
        .setPassword("XM8923&%"));

    private final CardVpClient cardVpClient = new CardVpClient(new CardVpConfig().setAppId("XIAOZHUANG88")
        .setAppKey("202412061725210mCSIs9H39NTWQkXMKhZqi247r6FGxb6biia7Yfb")
        .setEndpoint("api.carhomy.com"));

    @Override
    public CardPlatformEnum getCardPlatform() {
        return CardPlatformEnum.CARD_VP;
    }

    @Override
    public CardVpClient getClient() {
        return cardVpClient;
    }

    @Override
    public List<CardDO> getCardList(LocalDateTime start, LocalDateTime end, Integer syncPage) {
        log.info("======【{}】开始同步卡片数据======", getCardPlatform().getDescription());
        int page = 1;
        List<CardDO> cardList = new ArrayList<>();
        boolean hasMore = true;
        while (hasMore) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                CommonResponse<String> result = cardVpCrawlerClient.getCardList(page, DEFAULT_PAGE_SIZE, start, end);
                Document document = Jsoup.parse(result.getBody());
                Element table = document.getElementById("tableList");
                if (table == null) {
                    throw new CardVpUnauthorizedException();
                }
                Element tbody = table.child(1);
                Elements trs = tbody.children();
                hasMore = trs.size() >= DEFAULT_PAGE_SIZE;
                for (Element tr : trs) {
                    // No Data
                    if (tr.childrenSize() < 5) {
                        return new ArrayList<>();
                    }
                    CardDO cardRes = new CardDO();
                    cardRes.setPlatform(getCardPlatform());
                    String cardNumber = tr.child(2).text();
                    if (cardNumber.contains("*")) {
                        continue;
                    }
                    cardRes.setCardName(tr.child(1).text());
                    cardRes.setCvv(tr.child(4).text());
                    cardRes.setCardNumber(cardNumber);
                    String[] expireDateSplit = tr.child(3).text().split("/");
                    if (expireDateSplit.length == 2) {
                        cardRes.setExpireDate(expireDateSplit[1] + expireDateSplit[0]);
                    }
                    cardRes.setRemark(tr.child(7).text());
                    Element balanceTd = tr.child(5);
                    cardRes.setBalance(new BigDecimal(Objects.requireNonNull(balanceTd.firstElementChild())
                        .text()
                        .replace("$", "")));
                    cardRes.setUsedAmount(new BigDecimal(tr.child(6).text().replace("$", "")));
                    Element statusTd = tr.child(10);
                    cardRes.setStatus(statusTd.text().equals("Activate")
                        ? CardStatusEnum.NORMAL
                        : CardStatusEnum.FROZEN);
                    cardRes.setOpenTime(LocalDateTimeUtil.parse(tr.child(11).text(), "yyyy-MM-dd HH:mm:ss"));
                    cardList.add(cardRes);
                }
                if (syncPage != null && page == syncPage) {
                    break;
                }
                page++;
                Common.sleep(500);
            } catch (ThirdException e) {
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            } catch (CardVpUnauthorizedException e) {
                log.info("【{}】第{}页数据同步错误：登录过期", getCardPlatform().getDescription(), page);
                refreshCookie(cardVpCrawlerClient.getCookie());
            }
        }
        log.info("======【{}】卡片数据完成======", getCardPlatform().getDescription());
        return cardList;
    }

    @Override
    public List<CardBalanceDO> getCardBalanceList(LocalDateTime start, LocalDateTime end) {
        log.info("======【{}】开始同步卡片余额流水数据======", getCardPlatform().getDescription());
        int page = 1;
        List<CardBalanceDO> cardBalanceList = new ArrayList<>();
        boolean hasMore = true;
        while (hasMore) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                CommonResponse<String> result = cardVpCrawlerClient.getBillingList(page, DEFAULT_PAGE_SIZE, start, end);
                Document document = Jsoup.parse(result.getBody());
                Element table = document.getElementById("tableList");
                if (table == null) {
                    throw new CardVpUnauthorizedException();
                }
                Element tbody = table.child(1);
                Elements trs = tbody.children();
                hasMore = trs.size() >= DEFAULT_PAGE_SIZE;
                for (Element tr : trs) {
                    // No Data
                    if (tr.childrenSize() < 5) {
                        return new ArrayList<>();
                    }
                    CardBalanceDO cardBalanceDO = new CardBalanceDO();
                    cardBalanceDO.setPlatform(getCardPlatform());
                    String cardNumber = tr.child(4).text();
                    LocalDateTime transTime = LocalDateTimeUtil.parse(tr.child(0).text(), "yyyy-MM-dd HH:mm:ss");
                    String balance = tr.child(10).text().replace("$", "").replace("-", "");
                    String transId = LocalDateTimeUtil.format(transTime, "yyyyMMddHHmmss") + balance.replace(".", "");
                    // 避免新增记录时，历史记录被挤到下一页导致数据重复问题
                    CardBalanceDO exist = cardBalanceList.stream()
                        .filter(v -> v.getTransactionId().equalsIgnoreCase(transId))
                        .findFirst()
                        .orElse(null);
                    if (exist != null) {
                        continue;
                    }
                    String type = tr.child(1).text();
                    String opsType = tr.child(2).text();
                    cardBalanceDO.setCardNumber(cardNumber);
                    if ("OUT".equals(type)) {
                        cardBalanceDO.setType(CardBalanceTypeEnum.RECHARGE);
                    } else {
                        cardBalanceDO.setType(CardBalanceTypeEnum.WITHDRAW);
                    }
                    cardBalanceDO.setOriginType(type + "," + opsType);
                    cardBalanceDO.setAmount(new BigDecimal(tr.child(5).text().replace("$", "").replace("-", "")));
                    cardBalanceDO.setAfterAmount(new BigDecimal(balance));
                    cardBalanceDO.setTransTime(transTime);
                    cardBalanceDO.setTransactionId(transId);
                    cardBalanceList.add(cardBalanceDO);
                }
                page++;
                Common.sleep(500);
            } catch (ThirdException e) {
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            } catch (CardVpUnauthorizedException e) {
                log.info("【{}】第{}页数据同步错误：登录过期", getCardPlatform().getDescription(), page);
                refreshCookie(cardVpCrawlerClient.getCookie());
            }
        }
        log.info("======【{}】卡片余额流水数据完成======", getCardPlatform().getDescription());
        return cardBalanceList;
    }

    @Override
    public List<CardTransactionDO> getCardTransactionList(LocalDateTime start, LocalDateTime end, String status) {
        log.info("======【{}】开始同步卡片交易数据======", getCardPlatform().getDescription());
        if (start == null) {
            start = LocalDateTimeUtil.parse("2024-10-17 00:00:00", "yyyy-MM-dd HH:mm:ss");
        }
        if (end == null) {
            end = LocalDateTimeUtil.now();
        }
        List<CardTransactionDO> list = new ArrayList<>();
        start = start.minusHours(15);
        end  = end.minusHours(15);
        while (start.isBefore(end)) {
            int page = 1;
            int totalPages = 10;
            LocalDateTime sevenDayAfter = start.plusDays(1);
            LocalDateTime realEnd = sevenDayAfter.isBefore(end) ? sevenDayAfter : end;
            log.info("【{}】开始同步{}~{}数据...", getCardPlatform().getDescription(), start, realEnd);
            while (page <= totalPages) {
                log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
                CVPaginationRes<CVTransaction> result = cardVpClient.getTransactions(CVGetTransactionsRequest.builder()
                    .pageIndex(page)
                    .pageSize(DEFAULT_PAGE_SIZE)
                    .status(status)
                    .paydStart(LocalDateTimeUtil.format(start, "yyyy-MM-dd'T'HH:mm:ss"))
                    .paydEnd(LocalDateTimeUtil.format(realEnd, "yyyy-MM-dd'T'HH:mm:ss"))
                    .build()).getBody();
                totalPages = result.getNumbers();
                list.addAll(this.convertCardTransactionList(result.getItems()));
                page++;
            }
            start = sevenDayAfter;

        }
        log.info("======【{}】卡片交易数据完成======", getCardPlatform().getDescription());
        return list;
    }

    @Override
    public void rechargeCard(CardDO cardDO, BigDecimal amount) {
        String cardNumber = cardDO.getCardNumber();
        log.info("======【{}】开始充值卡片{}，金额：{}======", getCardPlatform().getDescription(), cardNumber, amount);
        cardVpClient.updateCardAmount(CVUpdateCardAmountRequest.builder()
            .cardnumber(cardNumber)
            .amount(amount)
            .build());
        log.info("======【{}】卡片{}充值完成======", getCardPlatform().getDescription(), cardNumber);
    }

    @Override
    public BigDecimal withdrawCard(CardDO cardDO, BigDecimal amount) {
        String cardNumber = cardDO.getCardNumber();
        // 要保留1美金，需要先查询账号限额
        BigDecimal withdrawAmount = BigDecimal.ZERO;
        log.info("======【{}】开始提现卡片{}======", getCardPlatform().getDescription(), cardNumber);
        if (amount == null) {
            CVCard card = cardVpClient.getCardInformation(cardNumber).getBody();
            log.info("【{}】卡片{}当前余额：{}", getCardPlatform().getDescription(), cardNumber, card.getBalance());
            if (card.getBalance().compareTo(BigDecimal.ONE) <= 0) {
                log.info("【{}】卡片{}低于1，无法提现", getCardPlatform().getDescription(), cardNumber);
                return withdrawAmount;
            } else {
                withdrawAmount = card.getBalance().subtract(BigDecimal.ONE);
            }
        } else {
            withdrawAmount = amount;
        }
        log.info("【{}】卡片{}提现金额：{}", getCardPlatform().getDescription(), cardNumber, withdrawAmount);
        cardVpClient.updateCardAmount(CVUpdateCardAmountRequest.builder()
            .cardnumber(cardNumber)
            .amount(withdrawAmount.negate())
            .build());
        log.info("======【{}】卡片{}提现完成======", getCardPlatform().getDescription(), cardNumber);
        return withdrawAmount;
    }

    @Override
    public CardDO openCard(JSONObject data) {
        CVCreateCardRequest request = CVCreateCardRequest.builder()
            .amount(BigDecimal.TEN)
            .referencenumber(data.getString("platformAdId"))
            .cardBin(data.getString("cardBin"))
            .purchaseType("ADS")
            .platform("facebook")
            .country("US")
            .build();
        CVCard card = cardVpClient.createCard(request).getBody();
        CardDO cardDO = new CardDO();
        cardDO.setCardNumber(card.getCardnumber());
        cardDO.setCardName(card.getCardname());
        cardDO.setExpireDate(card.getExpdate());
        cardDO.setPlatform(getCardPlatform());
        cardDO.setOpenTime(LocalDateTime.now());
        cardDO.setUsedAmount(card.getSpent());
        cardDO.setBalance(card.getBalance());
        cardDO.setCvv(card.getCvv());
        cardDO.setRemark(card.getRemark());
        return cardDO;
    }

    @Override
    public String getVerifyCode(CardDO card) {
        log.info("======【{}】开始获取卡片验证码{}======", getCardPlatform().getDescription(), card.getCardNumber());
        List<CVTransaction> result = cardVpClient.getCardTransactions(card.getCardNumber()).getBody();
        if (result == null || result.isEmpty()) {
            return "";
        }
        String verifyCode = "";
        for (CVTransaction cvTransaction : result) {
            if (FacebookUtils.isAuthTransaction(cvTransaction.getMerchantName())) {
                LocalDateTime time = LocalDateTimeUtil.parse(cvTransaction.getPaydate(), "yyyy-MM-dd HH:mm:ss")
                    .plusHours(15);
                verifyCode = "【%s】%s".formatted(LocalDateTimeUtil.format(time, "yyyy-MM-dd HH:mm:ss"), cvTransaction.getDesciption());
                break;
            }
        }
        log.info("======【{}】{}验证码获取结果：{}======", getCardPlatform().getDescription(), card.getCardNumber(), verifyCode);
        return verifyCode;
    }

    @Override
    public void updateRemark(CardDO card) {
        log.info("======【{}】开始更新卡片{}，备注：{}======", getCardPlatform().getDescription(), card.getCardNumber(), card.getRemark());
        if (StringUtils.isBlank(card.getRemark())) {
            return;
        }
        if (StringUtils.isBlank(cardVpCrawlerClient.getCookie())) {
            refreshCookie("");
        }
        cardVpCrawlerClient.updateCard(card.getCardNumber(), card.getRemark(), card.getCardName());
        log.info("======【{}】{}备注更新成功======", getCardPlatform().getDescription(), card.getCardNumber());
    }

    @Override
    public List<LabelValueResp<String>> getCardBinList() {
        if (StringUtils.isBlank(cardVpCrawlerClient.getCookie())) {
            refreshCookie("");
        }
        CommonResponse<String> result = cardVpCrawlerClient.getCardBulkAddPage();
        Document document = Jsoup.parse(result.getBody());
        List<Element> elements = document.getElementsByAttributeValue("name", "sourcecode");
        List<LabelValueResp<String>> list = new ArrayList<>();
        for (Element element : elements) {
            list.add(new LabelValueResp<>(element.attr("value"), element.attr("value")));
        }
        return list;
    }

    @Override
    public List<CardTransactionDO> convertCardTransactionList(JSONArray jsonArray) {
        List<CVTransaction> list = jsonArray.toJavaList(CVTransaction.class);
        return this.convertCardTransactionList(list);
    }

    @Override
    public CardDO getCardDetail(String cardId) {
        CVCard card = cardVpClient.getCardInformation(cardId).getBody();
        if (card == null) {
            return null;
        }
        CardDO cardDO = new CardDO();
        cardDO.setCardNumber(card.getCardnumber());
        cardDO.setPlatform(this.getCardPlatform());
        cardDO.setBalance(card.getBalance());
        cardDO.setStatus(null);
        cardDO.setRemark(card.getRemark());
        cardDO.setUsedAmount(card.getSpent());
        cardDO.setCardName(card.getCardname());
        cardDO.setCvv(card.getCvv());
        return cardDO;
    }

    @Override
    public CardDO getCardSensitiveDetail(String cardId) {
        return null;
    }

    @Override
    public void activeCard(CardDO card) {
        log.info("======【{}】开始激活卡片{}======", getCardPlatform().getDescription(), card.getCardNumber());
        cardVpClient.updateCardInformation(CVUpdateCardInformationRequest.builder()
            .cardnumber(card.getCardNumber())
            .status("R")
            .build());
        cardVpCrawlerClient.updateCard(card.getCardNumber(), card.getRemark(), card.getCardName());
        log.info("======【{}】{}激活成功======", getCardPlatform().getDescription(), card.getCardNumber());
    }

    @Override
    public CardDO getApplyCardResult(String requestId) {
        return null;
    }

    @Override
    public BigDecimal getCurrentBalance() {
        if (StringUtils.isBlank(cardVpCrawlerClient.getCookie())) {
            refreshCookie("");
        }
        CommonResponse<String> result = cardVpCrawlerClient.getAvailableAuthorizeList(1, DEFAULT_PAGE_SIZE);
        Document document = Jsoup.parse(result.getBody());
        document.getElementsByClass("rt-info");
        Elements classes = document.getElementsByClass("rt-info");
        if (classes.isEmpty()) {
            return BigDecimal.ZERO;
        }
        Element element = classes.get(0);
        Element textEle = element.child(1);
        String text = textEle.text();
        String balance = text.replace("$", "").replace(" USD", "");
        return new BigDecimal(balance).setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal getCardBalance(CardDO cardDO) {
        CVCard cvCard = cardVpClient.getCardInformation(cardDO.getCardNumber()).getBody();
        return cvCard.getBalance();
    }

    @Override
    public void inactiveCard(CardDO card) {
        log.info("======【{}】开始冻结卡片{}======", getCardPlatform().getDescription(), card.getCardNumber());
        cardVpClient.updateCardInformation(CVUpdateCardInformationRequest.builder()
            .cardnumber(card.getCardNumber())
            .status("P")
            .build());
        cardVpCrawlerClient.updateCard(card.getCardNumber(), card.getRemark(), card.getCardName());
        log.info("======【{}】{}冻结成功======", getCardPlatform().getDescription(), card.getCardNumber());
    }

    public List<CVTransfer> getTransferList() {
        log.info("======【{}】开始同步打款流水数据======", getCardPlatform().getDescription());
        int page = 1;
        List<CVTransfer> transferList = new ArrayList<>();
        boolean hasMore = true;
        while (hasMore) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                CommonResponse<String> result = cardVpCrawlerClient.getAvailableAuthorizeList(page, DEFAULT_PAGE_SIZE);
                Document document = Jsoup.parse(result.getBody());
                Element table = document.getElementById("tableList");
                if (table == null) {
                    throw new CardVpUnauthorizedException();
                }
                Element tbody = table.child(1);
                Elements trs = tbody.children();
                hasMore = trs.size() >= DEFAULT_PAGE_SIZE;
                for (Element tr : trs) {
                    // No Data
                    if (tr.childrenSize() < 5) {
                        return new ArrayList<>();
                    }
                    String amount = tr.child(4).text().replace("$", "").replace("-", "");
                    String fee = tr.child(5).text().replace("$", "").replace("-", "");
                    String balance = tr.child(6).text().replace("$", "").replace("-", "");
                    CVTransfer transfer = new CVTransfer();
                    transfer.setDate(tr.child(0).text());
                    transfer.setType(tr.child(1).text());
                    transfer.setCurrency(tr.child(2).text());
                    transfer.setWalletAddr(tr.child(3).text());
                    transfer.setAmount(new BigDecimal(amount));
                    transfer.setFee(new BigDecimal(fee));
                    transfer.setBalance(new BigDecimal(balance));
                    transferList.add(transfer);
                }
                page++;
                Common.sleep(500);
            } catch (ThirdException e) {
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            } catch (CardVpUnauthorizedException e) {
                log.info("【{}】第{}页数据同步错误：登录过期", getCardPlatform().getDescription(), page);
                refreshCookie(cardVpCrawlerClient.getCookie());
            }
        }
        log.info("======【{}】打款流水数据完成======", getCardPlatform().getDescription());
        return transferList;
    }

    private List<CardTransactionDO> convertCardTransactionList(List<CVTransaction> list) {
        List<CardTransactionDO> result = new ArrayList<>();
        for (CVTransaction item : list) {
            CardTransactionDO cardTransactionDO = new CardTransactionDO();
            cardTransactionDO.setPlatform(getCardPlatform());
            cardTransactionDO.setCardNumber(item.getCardnumber());
            cardTransactionDO.setTransactionId(item.getTradeNo());
            cardTransactionDO.setTransType(getCardTransactionType(item.getTransactionType()));
            cardTransactionDO.setOriginTransType(item.getTransactionType());
            cardTransactionDO.setTransStatus(getCardTransactionStatus(item.getStatus()));
            BigDecimal transAmount = item.getAmount().negate();
            cardTransactionDO.setTransAmount(transAmount);
            cardTransactionDO.setTransCurrency("USD");
            if (StringUtils.isNotBlank(item.getPaydate())) {
                LocalDateTime transTime = LocalDateTimeUtil.parse(item.getPaydate(), "yyyy-MM-dd HH:mm:ss");
                cardTransactionDO.setTransTime(transTime);
                cardTransactionDO.setChinaTime(transTime.plusHours(15));
            }
            cardTransactionDO.setTransDetail(item.getDesciption());
            result.add(cardTransactionDO);
        }
        return result;
    }

    private CardTransactionTypeEnum getCardTransactionType(String type) {
        switch (type) {
            case "authorization", "authorization.clearing" -> {
                return CardTransactionTypeEnum.AUTHORIZATION;
            }
            case "authorization.reversal", "authorization.reversal.issuerexpiration" -> {
                return CardTransactionTypeEnum.AUTHORIZATION_BACK;
            }
            case "refund.authorization.clearing" -> {
                return CardTransactionTypeEnum.REFUND;
            }
            default -> {
                return CardTransactionTypeEnum.OTHER;
            }
        }
    }

    private CardTransactionStatusEnum getCardTransactionStatus(String status) {
        switch (status) {
            case "PENDING" -> {
                return CardTransactionStatusEnum.PENDING;
            }
            case "COMPLETION" -> {
                return CardTransactionStatusEnum.SUCCESS;
            }
            case "DECLINED" -> {
                return CardTransactionStatusEnum.FAIL;
            }
            case "CLEARED" -> {
                return CardTransactionStatusEnum.CLEARED;
            }
            default -> {
                return CardTransactionStatusEnum.OTHER;
            }
        }
    }

    private synchronized void refreshCookie(String oldCookie) {
        if (StringUtils.isNotBlank(cardVpCrawlerClient.getCookie()) && !cardVpCrawlerClient.getCookie()
            .equals(oldCookie)) {
            return;
        }
        login();
    }

    private void login() {
        log.info("【{}】开始登录...", getCardPlatform().getDescription());
        CommonResponse<String> loginResult = cardVpCrawlerClient.login();
        String setCookie = loginResult.getHeaders().get("set-cookie");
        log.info("【{}】获取cookie成功：{}", getCardPlatform().getDescription(), setCookie);
        String cookie = StringUtils.substringBetween(setCookie, "[", ";");
        cardVpCrawlerClient.setCookie(cookie);
    }

    private static class CardVpUnauthorizedException extends RuntimeException {
        public CardVpUnauthorizedException() {
        }
    }
}
