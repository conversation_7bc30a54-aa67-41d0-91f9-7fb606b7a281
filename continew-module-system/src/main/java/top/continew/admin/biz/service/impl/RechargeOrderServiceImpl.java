/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.RechargeOrderCancelEvent;
import top.continew.admin.biz.event.RechargeOrderFinishEvent;
import top.continew.admin.biz.event.RechargeOrderHandleEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.mapper.RechargeOrderMapper;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.RechargeOrderDO;
import top.continew.admin.biz.model.query.RechargeOrderQuery;
import top.continew.admin.biz.model.req.RechargeOrderByCustomerReq;
import top.continew.admin.biz.model.req.RechargeOrderFinishReq;
import top.continew.admin.biz.model.req.RechargeOrderRechargeReq;
import top.continew.admin.biz.model.req.RechargeOrderReq;
import top.continew.admin.biz.model.resp.AdAccountCardOpsResultResp;
import top.continew.admin.biz.model.resp.RechargeOrderDetailResp;
import top.continew.admin.biz.model.resp.RechargeOrderResp;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.RechargeOrderService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.admin.system.service.FileService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

/**
 * 充值订单业务实现
 *
 * <AUTHOR>
 * @since 2024/12/30 17:09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RechargeOrderServiceImpl extends BaseServiceImpl<RechargeOrderMapper, RechargeOrderDO, RechargeOrderResp, RechargeOrderDetailResp, RechargeOrderQuery, RechargeOrderReq> implements RechargeOrderService {

    private final AdAccountService adAccountService;

    private final FileService fileService;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final TelegramChatIdConfig telegramChatIdConfig;

    @Override
    public PageResp<RechargeOrderResp> page(RechargeOrderQuery query, PageQuery pageQuery) {
        QueryWrapper<RechargeOrderDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<RechargeOrderResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return PageResp.build(page);
    }

    @Override
    public List<RechargeOrderResp> list(RechargeOrderQuery query, SortQuery sortQuery) {
        QueryWrapper<RechargeOrderDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, sortQuery);
        return baseMapper.selectCustomList(queryWrapper);
    }

    @Override
    protected <E> List<E> list(RechargeOrderQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<RechargeOrderDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, sortQuery);
        List<RechargeOrderResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    public synchronized void handleOrder(Long id) {
        RechargeOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(RechargeOrderStatusEnum.WAIT), "订单已被其他人处理");
        this.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
            .set(RechargeOrderDO::getHandleUser, UserContextHolder.getUserId())
            .set(RechargeOrderDO::getStatus, RechargeOrderStatusEnum.HANDLING)
            .set(RechargeOrderDO::getHandleTime, LocalDateTime.now())
            .eq(RechargeOrderDO::getId, id));
        SpringUtil.publishEvent(new RechargeOrderHandleEvent(order.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void finishOrder(RechargeOrderFinishReq req) {
        RechargeOrderDO order = this.getById(req.getId());
        CheckUtils.throwIf(!order.getStatus().equals(RechargeOrderStatusEnum.HANDLING), "订单未在处理中");
        CheckUtils.throwIf(order.getCardStatus().equals(RechargeOrderCardStatusEnum.WAIT), "订单未充值");
        CheckUtils.throwIf(!order.getHandleUser().equals(UserContextHolder.getUserId()), "仅限处理人进行操作");
        SpringUtil.publishEvent(new RechargeOrderFinishEvent(req));
    }

    @Override
    public synchronized void cancelOrder(Long id) {
        RechargeOrderDO order = this.getById(id);
        CheckUtils.throwIf(order.getHandleUser() != null && !order.getHandleUser()
            .equals(UserContextHolder.getUserId()), "仅限处理人进行操作");
        CheckUtils.throwIf(order.getStatus().equals(RechargeOrderStatusEnum.FINISH) || order.getStatus()
            .equals(RechargeOrderStatusEnum.CANCEL), "订单状态错误，无法取消");
        SpringUtil.publishEvent(new RechargeOrderCancelEvent(order.getId()));

    }

    @Override
    public synchronized void recharge(RechargeOrderRechargeReq req) {
        RechargeOrderDO order = this.getById(req.getId());
        CheckUtils.throwIf(!order.getStatus().equals(RechargeOrderStatusEnum.HANDLING), "订单未在处理中");
        CheckUtils.throwIf(order.getHandleUser() != null && !order.getHandleUser()
            .equals(UserContextHolder.getUserId()), "仅限处理人进行操作");
        CheckUtils.throwIf(!order.getCardStatus().equals(RechargeOrderCardStatusEnum.WAIT), "请勿重复充值");
        if (req.isAuto()) {
            AdAccountDO adAccount = adAccountService.getByPlatformAdId(order.getPlatformAdId());
            BigDecimal rechargeAmount;
            if (adAccount.getBillingThresholdCurrencyAmount().compareTo(BigDecimal.ZERO) <= 0) {
                rechargeAmount = new BigDecimal(100);
            } else {
                rechargeAmount = NumberUtil.min(order.getAmount(), adAccount.getBillingThresholdCurrencyAmount()
                    .multiply(new BigDecimal(3)));
            }
            AdAccountCardOpsResultResp resultResp = adAccountService.rechargeMasterCard(order.getPlatformAdId(), rechargeAmount, true);
            if (!resultResp.getIsSuccess()) {
                throw new BusinessException(resultResp.getMessage());
            }
            this.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
                .set(RechargeOrderDO::getCardStatus, RechargeOrderCardStatusEnum.AUTO)
                .eq(RechargeOrderDO::getId, order.getId()));
        } else {
            this.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
                .set(RechargeOrderDO::getCardStatus, RechargeOrderCardStatusEnum.MANUAL)
                .eq(RechargeOrderDO::getId, req.getId()));
        }
    }

    @Override
    public RechargeOrderDO getUnFinishOrder(String platformAdId) {
        return this.getOne(Wrappers.<RechargeOrderDO>lambdaQuery()
            .eq(RechargeOrderDO::getPlatformAdId, platformAdId)
            .in(RechargeOrderDO::getStatus, List.of(RechargeOrderStatusEnum.WAIT, RechargeOrderStatusEnum.HANDLING)));
    }

    @Override
    public void checkModifyLimit(String platformAdId,
                                 BigDecimal newLimitAmount,
                                 BigDecimal oldLimitAmount,
                                 String fbOpsId) {
        if (oldLimitAmount.compareTo(new BigDecimal(20)) <= 0) {
            log.info("【广告户限额检测】广告户{}当前限额：{}小于20，忽略计算", platformAdId, oldLimitAmount);
            oldLimitAmount = BigDecimal.ZERO;
        }
        BigDecimal increaseLimitAmount = newLimitAmount.subtract(oldLimitAmount);
        log.info("【广告户限额检测】广告户{}当前限额：{}，增长限额：{}", platformAdId, oldLimitAmount, increaseLimitAmount);
        RechargeOrderDO rechargeOrder = this.getUnFinishOrder(platformAdId);
        CheckUtils.throwIfNull(rechargeOrder, "当前广告户不存在进行中的充值订单");
        CheckUtils.throwIf(rechargeOrder.getFbCheckStatus()
            .equals(FbLimitCheckStatusEnum.FINISH), "当前广告户不存在未检测的充值订单");
        CheckUtils.throwIf(increaseLimitAmount.compareTo(rechargeOrder.getAmount()) != 0, "增长限额与后台充值金额不匹配，充值金额为：%s".formatted(NumberUtil.toStr(rechargeOrder.getAmount())));
        this.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
            .set(RechargeOrderDO::getFbCheckStatus, FbLimitCheckStatusEnum.SUCCESS)
            .set(RechargeOrderDO::getFbOpsId, fbOpsId)
            .eq(RechargeOrderDO::getId, rechargeOrder.getId()));
    }

    @Override
    public void generateCertificate(Long id) {
        RechargeOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(RechargeOrderStatusEnum.HANDLING), "订单未在处理中");
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(order.getPlatformAdId());
        CheckUtils.throwIfBlank(adAccount.getParentBrowserNo(), "当前广告户未绑定观察户");
        this.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
            .set(RechargeOrderDO::getCertificate, "")
            .eq(RechargeOrderDO::getId, id));
        threadPoolTaskExecutor.execute(() -> {
            JSONObject accountDetail = FacebookUtils.getAdAccountDetail(adAccount.getParentBrowserNo(), order.getPlatformAdId());
            BigDecimal limit = CommonUtils.divide100(accountDetail.getBigDecimal("spend_cap"), BigDecimal.ZERO);
            BigDecimal spend = CommonUtils.divide100(accountDetail.getBigDecimal("amount_spent"), BigDecimal.ZERO);
            String base64Str = FacebookUtils.generateBillingScreen(limit, spend);
            byte[] imageBytes = Base64.getDecoder().decode(base64Str);
            FileInfo fileInfo = fileService.uploadImage(imageBytes, "");
            this.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
                .set(RechargeOrderDO::getCertificate, fileInfo.getUrl())
                .eq(RechargeOrderDO::getId, id));
        });
    }

    @Override
    public void uploadModifyLimitCertificate(String platformAdId, String fbOpsId, String imageBase64) {
        RechargeOrderDO rechargeOrder = this.getOne(Wrappers.<RechargeOrderDO>lambdaQuery()
            .eq(RechargeOrderDO::getPlatformAdId, platformAdId)
            .eq(RechargeOrderDO::getFbOpsId, fbOpsId)
            .eq(RechargeOrderDO::getStatus, RechargeOrderStatusEnum.HANDLING));
        CheckUtils.throwIfNull(rechargeOrder, "充值订单不存在");
        String certificate = "";
        if (StringUtils.isNotBlank(imageBase64)) {
            byte[] imageBytes = Base64.getDecoder().decode(imageBase64);
            FileInfo fileInfo = fileService.uploadImage(imageBytes, "");
            certificate = fileInfo.getUrl();
        }
        this.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
            .set(RechargeOrderDO::getFbCheckStatus, FbLimitCheckStatusEnum.FINISH)
            .set(StringUtils.isNotBlank(certificate), RechargeOrderDO::getCertificate, certificate)
            .eq(RechargeOrderDO::getId, rechargeOrder.getId()));
    }

    @Override
    public void rechargeApply(RechargeOrderByCustomerReq req) {
        CustomerService customerService = SpringUtil.getBean(CustomerService.class);

        CustomerDO customer = customerService.getById(req.getCustomerId());
        CheckUtils.throwIfNull(customer, "未找到相关客户");
        AdAccountDO adAccount = adAccountService.getOne(Wrappers.<AdAccountDO>lambdaQuery()
            .eq(AdAccountDO::getPlatformAdId, req.getAdAccountId()));
        CheckUtils.throwIfNull(adAccount, "未找到相关广告户");
        CheckUtils.throwIf(!adAccount.getClearStatus()
            .equals(AdAccountClearStatusEnum.WAIT), "广告户已清零，请核对后充值");
        CheckUtils.throwIf(!adAccount.getAppealStatus()
            .equals(AdAccountAppealStatusEnum.WAIT) && !adAccount.getAppealStatus()
            .equals(AdAccountAppealStatusEnum.SUCCESS), "广告户已申诉，请核对后充值");
        CheckUtils.throwIf(adAccount.getSaleStatus()
            .equals(AdAccountSaleStatusEnum.INVALID), "广告户已作废，请核对后充值");
        AdAccountOrderService adAccountOrderService = SpringUtil.getBean(AdAccountOrderService.class);
        adAccountOrderService.checkExistOrder(req.getCustomerId(), req.getAdAccountId());
        RechargeOrderDO existOrder = this.getUnFinishOrder(req.getAdAccountId());
        CheckUtils.throwIfNotNull(existOrder, "请先处理之前的充值订单");
        RechargeOrderDO order = new RechargeOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("CZ"));
        order.setCustomerId(req.getCustomerId());
        order.setPlatformAdId(req.getAdAccountId());
        order.setAmount(req.getAmount());
        order.setStatus(RechargeOrderStatusEnum.WAIT);
        save(order);

        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
            .chatId(telegramChatIdConfig.getClientNotifyChatId())
            .text(String.format("""
                【待确认的充值操作】
                客户：%s
                广告户ID：%s
                金额：%s
                """, customer.getName(), req.getAdAccountId(), req.getAmount()))
            .build()));
    }
}