package top.continew.admin.biz.katai.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.katai.GzyClient;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.gzy.GzyConfig;
import top.continew.katai.gzy.model.req.*;
import top.continew.katai.gzy.model.resp.*;
import top.continew.katai.utils.Common;
import top.continew.starter.cache.redisson.util.RedisUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 11:24
 */
@Slf4j
@Service
public class GzyOpsStrategyImpl implements CardOpsStrategy<GzyClient> {
    private static final String GZY_TOKEN_KEY_PREFIX = "katai:gzy:token:";
    private String gzyTokenKey;
    public static final int DEFAULT_PAGE_SIZE = 100;
    private volatile GzyClient client;
    private final Object lock = new Object();
    private static final Duration CARD_NUMBER_CACHE_DURATION = Duration.ofDays(30);

    @PostConstruct
    public void init() {
        prodInit();
    }

    /**
     * 测试的配置
     */
    private void testInit() {
        GzyConfig config = new GzyConfig();
        config.setAppId("TMwYhkD1");
        config.setAppSecret("588b23ed2092ed58ed2392ae2c2cae5f92229286");
        config.setEndpoint("x-api1.uat.photontech.cc");
        String privateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALctBj9Ahy7+EtuT" + "GrPG8YpTqAoGwU9FegOemLblJk6NuzhYeNE/ZSLg8gbW9KEN+id4GCRVU5d8BSg7" + "KGSK7OqDW5eRRD4nQgvCH0JbcrA0dvCgkb3/0aw3Wo9U+iE6dtExSUm61cVn5ac1" + "mAC+1J82Jbs6I2u5DaWE08955kjfAgMBAAECgYAWdTO135BzZ30iZJe6gOKuRfFt" + "QUTEFk2OUgyBJ/kZWnEgyGAPr4kTzKZ/xiz57f/EhQnYsolsIi1zsEUUhp/jL5mt" + "n35LxOlni/vows7VwrLu88Ju82Ofq0WmfsRH0vabrLgifHELjhvsBgagtDuvOdKk" + "fTSAqCscF22AWdHvoQJBANzLt3ea8qwEafjIr16XUbSzapyBlAiVkOCewVZSYFMD" + "bNfgi8VVPtShWKLaL5yI6DBNuHA8nSQ6nhyPUQWTH28CQQDUYcQW3DMiHv/TnVg+" + "6rU05q1HJjh/9lSIK81YIISuBi9zQRGrmbIXiXCylNIobrcSBMwGb+Aty2fthYdN" + "KLWRAkBzsTr9LO+jOtzcQyvBAMELUke2oh8TMGuthwH/XUKjvNO3ei1CxRwbBkAP" + "dQwllYc0aEUQanJcnWpjxKTLqv95AkApZ9ob/tg1Ocf0kMOImKe1ZZe4vFndkL/Q" + "QIHrQYO8jdYoaTIxu2g+MHx5RIxod5VXfPnt81fIddSQOS00vNARAkBYiaejFvHh" + "xkeVPfqtMkaE22rBvyeVuzw0W7Uoioue8tnIfnrNaw3J70V7WRMDO/lkXoQ3wVhf" + "2UDYEZPrXUJt";
        config.setPrivateKey(privateKey);
        client = new GzyClient(config);
        // 设置测试环境的 Redis key
        gzyTokenKey = GZY_TOKEN_KEY_PREFIX + "test";
    }

    /**
     * 生产的配置
     */
    private void prodInit() {
        GzyConfig config = new GzyConfig();
        config.setAppId("oNnzJ06M");
        config.setAppSecret("f0373e3c37703e2a4b3f3a5f3f645f41373f3a3c");
        config.setEndpoint("x-api.photonpay.com");
        String privateKey = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAOcGeJwTzP15SPDS" + "AaJd1DoMboZZFx6Yw0AMh/PQIk5CihdFJhzCnpbZayD8ykvDyUGd2x0ZCJx8L7Fc" + "6sM0Cd+glEIp+jYKXM4Epw9wYkUv460XHAdVgq4QbGSiXdRtQYxqezeUxCKg95jb" + "JojDfO6SIeBzPe8JSSCwEyur19kvAgMBAAECgYEArKVZXMovVv1cVRubvQsBnp/L" + "uPq7HFJtF2BF+R6BhJqVN+6lHLkUawbv2CcRxqz5qd+d75DVrpvfyFv31Qj+loSe" + "X7CbesgS6keEwD0IYTz52qzIHTboR2gJhrJOtwwObyhv0IrKeRefN36hjnSXUmfO" + "vyO949mEFNmB8r9dfQECQQDz9yAj+lzJ6hcOyqTV6f2WajZHRF62/QrIi9CdecBN" + "jr0T8OdqAQO18FF+tFDVUNw0bZw8OmU4bgfBIFN6qHeHAkEA8mvvLhWzbl/ij+rt" + "kCNRW0b+WJFFb5NHXd4o0tmoxEJZS9c1WV8oxprPxwfaUCjbbIBLT4ikX1MedzwK" + "80urGQJBAI5xlxGKp8y44lefmBjSM180ipaX49lh637mq5qWsOJX2iwfd26cQwdi" + "1qJtM9kpCjmOWi+MRPdqvxpS/orafFcCQQDrh8aYpejxYiPD5GLv2gCfiHhyIKKv" + "5w49uy06vtLj6/rKXsusi3IYlC0vmQeZPjkCUBvbqEzbeOxuuF2DSI9xAkA/wCnp" + "Pr/826LF+aFwDiON3GIf66FMwjeREcQeCH9kpjYTEJIIh8ETuHCjbhO6cD6cdSKA" + "aFJ8tzk6I0WjOUIb";
        config.setPrivateKey(privateKey);
        client = new GzyClient(config);

        // 设置生产环境的 Redis key
        gzyTokenKey = GZY_TOKEN_KEY_PREFIX + "prod";
    }

    @Override
    public CardPlatformEnum getCardPlatform() {
        return CardPlatformEnum.PHOTON_PAY;
    }

    @Override
    public List<CardDO> getCardList(LocalDateTime start, LocalDateTime end, Integer syncPage) {
        log.info("======【{}】开始同步卡片数据======", getCardPlatform().getDescription());
        int page = 1;
        int totalPages = 10;
        List<CardDO> cardList = new ArrayList<>();

        while (page <= totalPages) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                GzyGetCardResponse result = getClient().getCards(GzyGetCardRequest.builder()
                    .pageIndex(page)
                    .pageSize(DEFAULT_PAGE_SIZE)
                    .build());
                totalPages = (int)Math.ceil((double)result.getTotal() / DEFAULT_PAGE_SIZE);

                for (GzyCardDetail item : result.getList()) {
                    CardDO cardDO = new CardDO();
                    cardDO.setCardNumber(item.getMaskCardNo());
                    cardDO.setPlatform(getCardPlatform());
                    cardDO.setBalance(null == item.getAvailableTransactionLimit()
                        ? BigDecimal.ZERO
                        : item.getAvailableTransactionLimit());
                    cardDO.setAssociation(item.getCardScheme());
                    cardDO.setUsedAmount(null == item.getTotalTransactionLimit()
                        ? BigDecimal.ZERO
                        : item.getTotalTransactionLimit());
                    cardDO.setRemark(item.getNickname());

                    if (StrUtil.isNotBlank(item.getCardStatus())) {
                        switch (item.getCardStatus()) {
                            case "normal":
                                cardDO.setStatus(CardStatusEnum.NORMAL);
                                break;
                            case "frozen":
                                cardDO.setStatus(CardStatusEnum.FROZEN);
                                break;
                            default:
                                cardDO.setStatus(CardStatusEnum.LOCKED);
                                break;
                        }
                    }
                    cardDO.setOpenTime(LocalDateTimeUtil.parse(item.getCreatedAt(), "yyyy-MM-dd'T'HH:mm:ss")
                        .plusHours(8));
                    cardDO.setPlatformCardId(item.getCardId());
                    cardList.add(cardDO);
                }

                if (syncPage != null && page == syncPage) {
                    break;
                }
                page++;
            } catch (ThirdException e) {
                if ("1002".equals(e.getCode())) {
                    log.error("光子易token失效");
                    RedisUtils.delete(gzyTokenKey);
                }
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            }
        }
        log.info("======【{}】卡片数据完成======", getCardPlatform().getDescription());
        return cardList;
    }

    @Override
    public List<CardBalanceDO> getCardBalanceList(LocalDateTime start, LocalDateTime end) {
        log.info("======【{}】开始同步卡片余额流水数据======", getCardPlatform().getDescription());
        int page = 1;
        int totalPages = 10;
        List<CardBalanceDO> cardBalanceList = new ArrayList<>();

        if (start != null && end != null) {
            start = start.minusHours(8);
            end = end.minusHours(8);
        }

        while (page <= totalPages) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                GzyShareCardTxnLimitRequest.GzyShareCardTxnLimitRequestBuilder builder = GzyShareCardTxnLimitRequest.builder()
                    .pageIndex(page)
                    .pageSize(DEFAULT_PAGE_SIZE);

                if (start != null && end != null) {
                    builder.createdAtStart(LocalDateTimeUtil.format(start, "yyyy-MM-dd'T'HH:mm:ss"))
                        .createdAtEnd(LocalDateTimeUtil.format(end, "yyyy-MM-dd'T'HH:mm:ss"));
                }

                GzyShareCardTxnLimitResponse result = getClient().getShareCardTxnLimitDetail(builder.build());
                totalPages = (int)Math.ceil((double)result.getTotal() / DEFAULT_PAGE_SIZE);

                for (GzyShareCardTxnLimitDetail item : result.getList()) {
                    CardBalanceDO cardBalanceDO = new CardBalanceDO();
                    cardBalanceDO.setPlatform(getCardPlatform());
                    cardBalanceDO.setCardNumber(StrUtil.EMPTY);
                    cardBalanceDO.setPlatformCardId(item.getCardId());

                    // 根据变动金额判断类型
                    BigDecimal changeAmount = item.getChangeAmount();
                    cardBalanceDO.setOriginType(item.getTransactionType());
                    if (changeAmount.compareTo(BigDecimal.ZERO) > 0) {
                        cardBalanceDO.setType(CardBalanceTypeEnum.RECHARGE);

                    } else {
                        cardBalanceDO.setType(CardBalanceTypeEnum.WITHDRAW);
                    }

                    cardBalanceDO.setAmount(changeAmount.abs());
                    cardBalanceDO.setAfterAmount(item.getAvailableTransactionLimit());
                    cardBalanceDO.setTransTime(LocalDateTimeUtil.parse(item.getCreatedAt(), "yyyy-MM-dd'T'HH:mm:ss")
                        .plusHours(8));
                    cardBalanceDO.setTransactionId(item.getTransactionId());
                    cardBalanceList.add(cardBalanceDO);
                }

                page++;
            } catch (ThirdException e) {
                if ("1002".equals(e.getCode())) {
                    log.error("光子易token失效");
                    RedisUtils.delete(gzyTokenKey);
                }
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            }
        }

        log.info("======【{}】卡片余额流水数据完成======", getCardPlatform().getDescription());
        return cardBalanceList;
    }

    @Override
    public List<CardTransactionDO> getCardTransactionList(LocalDateTime start, LocalDateTime end, String status) {
        log.info("======【{}】开始同步卡片交易数据======", getCardPlatform().getDescription());
        int page = 1;
        int totalPages = 10;
        if (start == null) {
            start = LocalDateTimeUtil.parse("2025-03-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        }
        if (end == null) {
            end = LocalDateTimeUtil.now();
        }

        start = start.minusHours(8);
        end = end.minusHours(8);

        List<CardTransactionDO> list = new ArrayList<>();

        while (page <= totalPages) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                GzyGetTransactionRequest.GzyGetTransactionRequestBuilder builder = GzyGetTransactionRequest.builder()
                    .pageIndex(page)
                    .pageSize(DEFAULT_PAGE_SIZE)
                    .createdAtStart(LocalDateTimeUtil.format(start, "yyyy-MM-dd'T'HH:mm:ss"))
                    .createdAtEnd(LocalDateTimeUtil.format(end, "yyyy-MM-dd'T'HH:mm:ss"));

                if (StrUtil.isNotBlank(status)) {
                    builder.status(status);
                }

                GzyGetTransactionResponse result = getClient().getTransactions(builder.build());
                totalPages = (int)Math.ceil((double)result.getTotal() / DEFAULT_PAGE_SIZE);

                list.addAll(this.convertCardTransactionList(result.getList()));

                page++;
            } catch (ThirdException e) {
                if ("1002".equals(e.getCode())) {
                    log.error("光子易token失效");
                    RedisUtils.delete(gzyTokenKey);
                }
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            }
        }

        log.info("======【{}】卡片交易数据完成======", getCardPlatform().getDescription());
        return list;
    }

    @Override
    public void rechargeCard(CardDO cardDO, BigDecimal amount) {
        log.info("======【{}】开始充值卡片{}，金额：{}======", getCardPlatform().getDescription(), cardDO.getPlatformCardId(), amount);
        GzyUpdateCardRequest updateReq = GzyUpdateCardRequest.builder()
            .requestId(UUID.randomUUID().toString())
            .cardId(cardDO.getPlatformCardId())
            .transactionLimitType("limited")
            .transactionLimit(amount)
            .transactionLimitChangeType("increase")
            .build();
        try {
            getClient().updateCard(updateReq);
            log.info("======【{}】卡片{}充值完成======", getCardPlatform().getDescription(), cardDO.getPlatformCardId());

        } catch (ThirdException e) {
            if ("1002".equals(e.getCode())) {
                log.error("光子易token失效");
                RedisUtils.delete(gzyTokenKey);

            }
            throw e;
        }
    }

    @Override
    public void activeCard(CardDO cardDO) {
        log.info("======【{}】开始激活卡片：{}", getCardPlatform().getDescription(), cardDO.getPlatformCardId());
        GzyFreezeCardRequest request = GzyFreezeCardRequest.builder().build();
        request.setCardId(cardDO.getPlatformCardId());
        request.setRequestId(UUID.randomUUID().toString());
        request.setStatus("unfreeze");
        try {
            getClient().freezeCard(request);
            log.info("======【{}】卡片{}激活完成======", getCardPlatform().getDescription(), cardDO.getPlatformCardId());

        } catch (ThirdException e) {
            if ("1002".equals(e.getCode())) {
                log.error("光子易token失效");
                RedisUtils.delete(gzyTokenKey);

            }
            throw e;
        }
    }

    @Override
    public void inactiveCard(CardDO cardDO) {
        log.info("======【{}】开始冻结卡片：{}", getCardPlatform().getDescription(), cardDO.getPlatformCardId());
        GzyFreezeCardRequest request = GzyFreezeCardRequest.builder().build();
        request.setCardId(cardDO.getPlatformCardId());
        request.setRequestId(UUID.randomUUID().toString());
        request.setStatus("freeze");
        try {
            getClient().freezeCard(request);
            log.info("======【{}】卡片{}冻结完成======", getCardPlatform().getDescription(), cardDO.getPlatformCardId());

        } catch (ThirdException e) {
            if ("1002".equals(e.getCode())) {
                log.error("光子易token失效");
                RedisUtils.delete(gzyTokenKey);
            }
            throw e;
        }
    }

    @Override
    public BigDecimal withdrawCard(CardDO cardDO, BigDecimal amount) {
        String cardNumber = cardDO.getCardNumber();
        BigDecimal withdrawAmount;
        log.info("======【{}】开始提现卡片{}======", getCardPlatform().getDescription(), cardNumber);

        if (amount == null) {
            GzyCardDetail card = getClient().getCardDetail(cardDO.getPlatformCardId());

            log.info("【{}】卡片{}当前余额：{}", getCardPlatform().getDescription(), cardNumber, card.getAvailableTransactionLimit());
            if (null == card.getAvailableTransactionLimit()) {
                log.info("【{}】卡片{}没有限额，无法提现，是否限额：{}", getCardPlatform().getDescription(), cardNumber, card.getTransactionLimitType());
                return BigDecimal.ZERO;
            }

            if (card.getAvailableTransactionLimit().compareTo(BigDecimal.ZERO) <= 0) {
                log.info("【{}】卡片{}不大于零，无法提现，是否限额：{}", getCardPlatform().getDescription(), cardNumber, card.getTransactionLimitType());
                return BigDecimal.ZERO;
            }

            withdrawAmount = card.getAvailableTransactionLimit();

        } else {
            withdrawAmount = amount;
        }

        GzyUpdateCardRequest updateReq = GzyUpdateCardRequest.builder()
            .requestId(UUID.randomUUID().toString())
            .cardId(cardDO.getPlatformCardId())
            .transactionLimitType("limited")
            .transactionLimit(withdrawAmount)
            .transactionLimitChangeType("decrease")
            .build();

        try {
            getClient().updateCard(updateReq);
            log.info("======【{}】卡片{}提现完成======", getCardPlatform().getDescription(), cardNumber);

        } catch (ThirdException e) {
            if ("1002".equals(e.getCode())) {
                log.error("光子易token失效");
                RedisUtils.delete(gzyTokenKey);
            }

            throw e;
        }

        return withdrawAmount;
    }

    @Override
    public CardDO openCard(JSONObject data) {
        String requestId = data.getString("requestId");
        String cardholderId = data.getString("cardHolderId");
        GzyCreateCardRequest request = GzyCreateCardRequest.builder()
            .cardBin(data.getString("cardBin"))
            .cardCurrency("USD")
            .cardType("share")
            .requestId(requestId)
            .transactionLimitType("limited")
            //不填写则使用默认持卡人
            .cardholderId(cardholderId)
            //日交易限额
            .maxOnDaily(100000)
            //单笔交易最大限额
            .maxOnPercent(100000)
            //月交易限额
            .maxOnMonthly(100000)
            .cardScheme(data.getString("cardScheme"))
            .transactionLimit(BigDecimal.TEN)
            .build();

        GzyCreateCardResponse response = getClient().createCard(request);
        log.info("光子易开卡创建结果：{}", response);
        if ("succeed".equals(response.getStatus())) {
            GzyCardDetail card = response.getCardDetail();

            CardDO cardDO = new CardDO();
            cardDO.setCardNumber(card.getCardNo());
            cardDO.setPlatform(getCardPlatform());
            cardDO.setBalance(null != card.getAvailableTransactionLimit()
                ? card.getAvailableTransactionLimit()
                : BigDecimal.TEN);
            cardDO.setExpireDate(card.getExpirationDate());
            cardDO.setAssociation(card.getCardScheme());
            cardDO.setCvv(card.getCvv());
            cardDO.setStatus(CardStatusEnum.NORMAL);
            LocalDateTime createdAt = LocalDateTimeUtil.parse(card.getCreatedAt(), "yyyy-MM-dd'T'HH:mm:ss");
            if (createdAt != null) {
                cardDO.setOpenTime(createdAt.plusHours(8));
            }
            cardDO.setPlatformCardId(card.getCardId());
            cardDO.setPlatformCardHolderId(cardholderId);

            if (StrUtil.isBlank(cardDO.getCardNumber())) {
                // 获取卡片敏感信息
                GzyCardSensitiveDetail sensitiveDetail = getClient().getCvv(card.getCardId());
                if (sensitiveDetail != null) {
                    cardDO.setCvv(sensitiveDetail.getCvv());
                    cardDO.setExpireDate(sensitiveDetail.getExpirationDate());
                    cardDO.setCardNumber(sensitiveDetail.getCardNo());
                }
            }

            return cardDO;
        }

        if ("failed".equals(response.getStatus())) {
            throw new BusinessException("开卡失败");
        }

        if ("pending".equals(response.getStatus()) || "pending_recharge".equals(response.getStatus())) {
            log.info("光子易开卡中,requestId:{}", request.getRequestId());
            //休眠1s，尝试获取一次
            Common.sleep(1000);
            return getApplyCardResult(requestId);
        }

        throw new BusinessException("开卡失败");
    }

    @Override
    public CardDO getApplyCardResult(String requestId) {
        GzyGetRequestResultResponse resultResp = getClient().getRequestResult(GzyGetRequestResultRequest.builder()
            .type("apply_card")
            .requestId(requestId)
            .build());
        log.info("光子易开卡结果：{}", resultResp);
        if ("succeed".equals(resultResp.getStatus())) {
            GzyCardDetail card = resultResp.getCardDetail();

            CardDO cardDO = new CardDO();
            cardDO.setCardNumber(card.getCardNo());
            cardDO.setPlatform(getCardPlatform());
            cardDO.setBalance(null != card.getAvailableTransactionLimit()
                ? card.getAvailableTransactionLimit()
                : BigDecimal.TEN);
            cardDO.setExpireDate(card.getExpirationDate());
            cardDO.setAssociation(card.getCardScheme());
            cardDO.setCvv(card.getCvv());
            cardDO.setStatus(CardStatusEnum.NORMAL);
            cardDO.setOpenTime(LocalDateTimeUtil.parse(card.getCreatedAt(), "yyyy-MM-dd'T'HH:mm:ss"));
            cardDO.setPlatformCardId(card.getCardId());

            if (StrUtil.isBlank(cardDO.getCardNumber())) {
                // 获取卡片敏感信息
                GzyCardSensitiveDetail sensitiveDetail = getClient().getCvv(card.getCardId());
                if (sensitiveDetail != null) {
                    cardDO.setCvv(sensitiveDetail.getCvv());
                    cardDO.setExpireDate(sensitiveDetail.getExpirationDate());
                    cardDO.setCardNumber(sensitiveDetail.getCardNo());
                }
            }

            return cardDO;
        }

        if ("failed".equals(resultResp.getStatus())) {
            throw new BusinessException("开卡失败");
        }

        if ("pending".equals(resultResp.getStatus()) || "pending_recharge".equals(resultResp.getStatus())) {
            CardDO cardDO = new CardDO();
            cardDO.setCardNumber(requestId);
            cardDO.setPlatform(getCardPlatform());
            cardDO.setBalance(BigDecimal.ZERO);
            cardDO.setExpireDate(StrUtil.EMPTY);
            cardDO.setAssociation(StrUtil.EMPTY);
            cardDO.setCvv(StrUtil.EMPTY);
            cardDO.setStatus(CardStatusEnum.PENDING);
            cardDO.setPlatformCardId(StrUtil.EMPTY);
            return cardDO;
        }

        return null;
    }

    @Override
    public String getVerifyCode(CardDO card) {
        int page = 1;
        String verifyCode = "";

        try {
            log.info("======【{}】开始获取卡片验证码{}======第{}页", getCardPlatform().getDescription(), card.getCardNumber(), page);
            GzyGetTransactionResponse result = getClient().getTransactions(GzyGetTransactionRequest.builder()
                .cardId(card.getPlatformCardId())
                .transactionType("auth")
                .pageIndex(page)
                .pageSize(DEFAULT_PAGE_SIZE)
                .build());

            if (result == null || CollUtil.isEmpty(result.getList())) {
                return "";
            }

            // 查找匹配的交易记录
            for (GzyTransactionItem item : result.getList()) {
                if (null != item.getMerchantNameLocation()) {
                    if (FacebookUtils.isAuthTransaction(item.getMerchantNameLocation())) {
                        LocalDateTime time = LocalDateTimeUtil.parse(item.getCreatedAt(), "yyyy-MM-dd'T'HH:mm:ss");
                        time = null != time ? time.plusHours(8) : null;
                        verifyCode = "【%s】%s".formatted(LocalDateTimeUtil.format(time, "yyyy-MM-dd HH:mm:ss"), item.getMerchantNameLocation());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("【{}】获取卡片{}验证码失败：{}", getCardPlatform().getDescription(), card.getCardNumber(), e.getMessage());
            return "";
        }
        log.info("======【{}】{}验证码获取结果：{}======", getCardPlatform().getDescription(), card.getCardNumber(), verifyCode);
        return verifyCode;
    }

    @Override
    public void updateRemark(CardDO card) {
        log.info("======【{}】开始更新卡片{}，备注：{}======", getCardPlatform().getDescription(), card.getCardNumber(), card.getRemark());
        if (StringUtils.isBlank(card.getRemark())) {
            return;
        }

        try {
            getClient().updateCard(GzyUpdateCardRequest.builder()
                .requestId(UUID.randomUUID().toString())
                .cardId(card.getPlatformCardId())
                .nickname(card.getRemark())
                .build());
            log.info("======【{}】{}备注更新成功======", getCardPlatform().getDescription(), card.getCardNumber());

        } catch (ThirdException e) {
            if ("1002".equals(e.getCode())) {
                log.error("光子易token失效");
                RedisUtils.delete(gzyTokenKey);
            }
            throw e;
        }

    }

    @Override
    public List<LabelValueResp<String>> getCardBinList() {
        List<GzyCardBinResponse> cardBins = getClient().getCardBin();

        List<LabelValueResp<String>> list = new ArrayList<>();
        for (GzyCardBinResponse cardBin : cardBins) {
            if (StrUtil.isBlank(cardBin.getCardBin()) || cardBin.getCardBin().equals("52737560")) {
                continue;
            }



            LabelValueResp<String> labelValue = new LabelValueResp<>(cardBin.getCardBin(), cardBin.getCardBin());
            Map<String, Object> map = new HashMap<>();
            map.put("cardScheme", cardBin.getCardScheme());
            map.put("remainingAvailableCard", cardBin.getRemainingAvailableCard());
            map.put("availableCard", cardBin.getAvailableCard());
            labelValue.setExtra(map);
            list.add(labelValue);
        }
        return list;
    }

    @Override
    public List<CardTransactionDO> convertCardTransactionList(JSONArray jsonArray) {
        List<GzyTransactionItem> list = jsonArray.toJavaList(GzyTransactionItem.class);
        return this.convertCardTransactionList(list);
    }

    @Override
    public BigDecimal getCurrentBalance() {
        GzyGetAccountResponse gzyGetAccountResponse = getClient().getAccount(GzyGetAccountRequest.builder()
            .currency("USD")
            .build());
        return gzyGetAccountResponse.getRealTimeBalance();
    }

    @Override
    public BigDecimal getCardBalance(CardDO cardDO) {
        GzyCardDetail detail = getClient().getCardDetail(cardDO.getPlatformCardId());
        return detail.getAvailableTransactionLimit();
    }

    private List<CardTransactionDO> convertCardTransactionList(List<GzyTransactionItem> list) {
        List<CardTransactionDO> resultList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (GzyTransactionItem item : list) {
                CardTransactionDO cardTransactionDO = new CardTransactionDO();
                cardTransactionDO.setPlatform(getCardPlatform());
                cardTransactionDO.setCardNumber(StrUtil.EMPTY);
                cardTransactionDO.setPlatformCardId(item.getCardId());
                cardTransactionDO.setTransactionId(item.getTransactionId());
                cardTransactionDO.setOriginTransactionId(item.getOriginTransactionId());
                cardTransactionDO.setTransType(getCardTransactionType(item.getTransactionType()));
                cardTransactionDO.setOriginTransType(item.getTransactionType() + "," + item.getStatus());
                cardTransactionDO.setTransStatus(getCardTransactionStatus(item.getStatus()));
                BigDecimal transAmount = item.getTxnPrincipalChangeAmount() == null
                    ? item.getTransactionAmount()
                    : item.getTxnPrincipalChangeAmount();
                if (item.getStatus().equals("failed")) {
                    transAmount = item.getTransactionAmount().negate();
                }
                cardTransactionDO.setTransAmount(transAmount);
                cardTransactionDO.setTransCurrency(item.getTransactionCurrency());
                cardTransactionDO.setTransTime(LocalDateTimeUtil.parse(item.getCreatedAt(), "yyyy-MM-dd'T'HH:mm:ss"));
                cardTransactionDO.setChinaTime(null != cardTransactionDO.getTransTime()
                    ? cardTransactionDO.getTransTime().plusHours(8)
                    : null);
                cardTransactionDO.setTransDetail(item.getMerchantNameLocation());
                cardTransactionDO.setRemark(item.getMsg());
                resultList.add(cardTransactionDO);
            }
        }

        return resultList;
    }

    @Override
    public CardDO getCardDetail(String cardId) {
        GzyCardDetail gzyCardDetail = getClient().getCardDetail(cardId);
        //1002 表示token失效
        if (null != gzyCardDetail) {
            CardDO cardDO = new CardDO();
            cardDO.setCardNumber(gzyCardDetail.getCardNo());
            cardDO.setPlatformCardHolderId(gzyCardDetail.getCardholderId());
            cardDO.setPlatformCardId(gzyCardDetail.getCardId());
            cardDO.setBalance(gzyCardDetail.getAvailableTransactionLimit());
            if (StrUtil.isNotBlank(gzyCardDetail.getCardStatus())) {
                switch (gzyCardDetail.getCardStatus()) {
                    case "normal":
                        cardDO.setStatus(CardStatusEnum.NORMAL);
                        break;
                    case "frozen":
                        cardDO.setStatus(CardStatusEnum.FROZEN);
                        break;
                    default:
                        cardDO.setStatus(CardStatusEnum.LOCKED);
                        break;
                }
            }
            return cardDO;
        }

        return null;
    }

    @Override
    public CardDO getCardSensitiveDetail(String cardId) {
        GzyCardSensitiveDetail detail = getClient().getCvv(cardId);
        if (null != detail) {
            CardDO cardDO = new CardDO();
            cardDO.setCvv(detail.getCvv());
            cardDO.setExpireDate(detail.getExpirationDate());
            cardDO.setCardNumber(detail.getCardNo());
            return cardDO;
        }

        return null;

    }

    /**
     * 获取GzyClient客户端
     *
     * @return GzyClient实例
     */
    @Override
    public GzyClient getClient() {
        //从缓存中获取token
        //        String token = RedisUtils.get(gzyTokenKey);
        //        if (StrUtil.isBlank(token)) {
        //            synchronized (lock) {
        //                // 双重检查，避免重复获取 token
        //                token = RedisUtils.get(gzyTokenKey);
        //                if (StrUtil.isBlank(token)) {
        //                    GzyTokenResponse resp = client.getToken();
        //                    token = resp.getToken();
        //                    long expiresIn = resp.getExpiresIn();
        //                    //缓存过期时间设置为1个半小时
        //                    RedisUtils.set(gzyTokenKey, token, Duration.ofSeconds(5400));
        //                    log.info("光子易-获取token成功，有效期：{}", expiresIn);
        //                }
        //                client.setAccessToken(token);
        //            }
        //        } else {
        //            synchronized (lock) {
        //                client.setAccessToken(token);
        //            }
        //        }
        return client;
    }

    /**
     * 获取交易类型
     */
    private CardTransactionTypeEnum getCardTransactionType(String type) {
        if (StrUtil.isBlank(type)) {
            return CardTransactionTypeEnum.OTHER;
        }
        switch (type.toLowerCase()) {
            case "auth":
                return CardTransactionTypeEnum.AUTHORIZATION;
            case "verification":
                return CardTransactionTypeEnum.VERIFICATION;
            case "corrective_auth":
            case "void":
                return CardTransactionTypeEnum.AUTHORIZATION_BACK;
            case "refund":
                return CardTransactionTypeEnum.REFUND;
            default:
                return CardTransactionTypeEnum.OTHER;
        }
    }

    /**
     * 获取交易状态
     */
    private CardTransactionStatusEnum getCardTransactionStatus(String status) {
        if (StrUtil.isBlank(status)) {
            return CardTransactionStatusEnum.OTHER;
        }

        //Enum: "pending" "authorized" "succeed" "failed" "void" "processing"
        //状态
        return switch (status.toLowerCase()) {
            case "succeed" -> CardTransactionStatusEnum.SUCCESS;
            case "failed" -> CardTransactionStatusEnum.FAIL;
            case "void" -> CardTransactionStatusEnum.REVOKE;
            case "pending", "authorized", "processing" -> CardTransactionStatusEnum.PENDING;
            default -> CardTransactionStatusEnum.OTHER;
        };
    }
}
