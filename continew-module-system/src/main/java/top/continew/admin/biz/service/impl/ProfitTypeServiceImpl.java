package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.ProfitTypeMapper;
import top.continew.admin.biz.model.entity.ProfitTypeDO;
import top.continew.admin.biz.model.query.ProfitTypeQuery;
import top.continew.admin.biz.model.req.ProfitTypeReq;
import top.continew.admin.biz.model.resp.ProfitTypeDetailResp;
import top.continew.admin.biz.model.resp.ProfitTypeResp;
import top.continew.admin.biz.service.ProfitTypeService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.util.List;

/**
 * 利润类型业务实现
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Service
@RequiredArgsConstructor
public class ProfitTypeServiceImpl extends BaseServiceImpl<ProfitTypeMapper, ProfitTypeDO, ProfitTypeResp, ProfitTypeDetailResp, ProfitTypeQuery, ProfitTypeReq> implements ProfitTypeService {
    @Override
    public List<Long> selectFinanceTypeIds() {
        return this.list(Wrappers.<ProfitTypeDO>lambdaQuery().like(ProfitTypeDO::getCat, "finance"))
            .stream()
            .map(ProfitTypeDO::getId)
            .toList();
    }

}