/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.katai.strategy.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.katai.HuiTongClient;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.huitong.HuiTongConfig;
import top.continew.katai.huitong.model.HTPaginationRes;
import top.continew.katai.huitong.model.card.HTCard;
import top.continew.katai.huitong.model.card.HTGetCardPageRequest;
import top.continew.katai.huitong.model.card.HTRechargeCardRequest;
import top.continew.katai.huitong.model.card.HTWithdrawCardRequest;
import top.continew.katai.huitong.model.customer.HTBalanceRecord;
import top.continew.katai.huitong.model.customer.HTGetBalanceRecordRequest;
import top.continew.katai.huitong.model.transaction.HTGetTransactionsRequest;
import top.continew.katai.huitong.model.transaction.HTTransaction;
import top.continew.katai.utils.Common;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class HuiTongOpsStrategyImpl implements CardOpsStrategy<HuiTongClient> {

    public static final int DEFAULT_PAGE_SIZE = 100;

    private final HuiTongClient client = new HuiTongClient(new HuiTongConfig().setCustomerToken("50962AA05A2979E4")
        .setSecret("76E74E1326DBF52A2F561C8E788AC0B113922E16A7F2C975EEDAAD9D34F1348")
        .setServerPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuUIjkAKkRvJ+SwfiUgIf6PQvCx9UbJpmfZ6E7DQaJ3uITID4G9BEGKHEGMaGrBm17xct/ySCrzQq53VghkK/CO4nCnPA8xfP9yuJsQiftXPoc7f76Y7D0m//vCK6kS3ig0CGAOgtPj8QI6wKWqZjY29ZFBgAhlGS4vp4Oa9xNdh+5NuhFUtmkzSvVJyVA1YcntqdGgNeK/6niObBWCDVXlC02h5ixu+Cy2+G6D6xKc/S6UlfmxnsvvQhRvxj/wKwqaiAz9ujiPEc0WWM0j1kUfb4aC3gbDw3JikHeCnt8jCTP0Z5LchGFO0ZDF+e//g48woOIfuBgosTt8iij18QsQIDAQAB")
        .setCustomerPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQD2ygRcp+UZNdw9t83bRqe86tmHCbD3OtH3oOA/7KC9Y/IPauFTRh25Xtk1kE08c7RapOvMk+5ZN5eyeARevAoRC/UHcVB0wLf+BKSQmzJjwELyaXUKBTbXvptVn8OrVmQZCxgZcMHdDbjBOIvJ2AgQi2hwgoKbXguZTjf56LpLHVJMl73g6n2MYiQPltiL/BUb9zzW1eFVvBEPIyy/9BvJMqiqQadXcp3XJA+W+Z9wyOn8cPskCkhVxGImk0PXZaWARPQWkauNPxVIBgzcsZ9AjaVi2Hp4Ne+0RzOqh1ppZJfYonCXZX8MvzoQmDvvyo82xUcYiFcoNgIkrpPd7rqpAgMBAAECggEBAI/6vl68SUnTBYIwaPZkG1p5fZ1uBwPGGlepUQuaZxD3bXKXPYxS3OaYMoswfA8nxfIYQ8WgDK+aYnNp18rZU9osKrFD59el0/JsDQKcuOI/eg9pfTHyZtOpzYA/7G1awp1lvpKhGUl5Ru/bqJqC4fmdC7qQOdihHo07eCbiI0XMWTSF0c+zuzzPz3iToSB1ZLpYT0O1MSrsiDmDBNEbjIKtT/a4Gsp/MvaERs/AtRA0OcEXL2uemYM20CdHCktKTRrFtdXL3GwIgWPoEth7RRFXlJJTtXZa1aNYGs0y0Nljh4755HiEH2ThAVrrh+pfA9M3jiS3tbm03PR6tu3Und0CgYEA/xu5HboDXpJoUHiVYW9KEqi6Ojm2bxiWorDr8MzaE95rX4Y1NYsa+duY/iGbpc6TGsdSyPX+FjF2qxcsmyIVqQ4MQvNnVLnIeGoHkwPfdcggxkUPo1OlKxO8pxwsZrBHuHWMtqjYhfLznPc3HL9Az2ZSg6sd8T4kUTE2Rufhsd8CgYEA96bZiOpRoeU4PwWxqhPA4rV2btGO9aY/GbXsoaVPyNFbaUdYs76F5XBnXa3aXvFgBj+hVFPxlKO+CQZrz4hqYjI8AkGBIyh2Xm+oJ0MAysJmGKoLm9PZm5bvWsRL2Mk2eDhrJMgU2BlUqTYwy5dOikvLt28eA3tgT7Db91YUdHcCgYEAhEEjHz4oMQdF/TIGf5TqmfHPWhBBfCcLMRz3tMb+5/4PPF6fhzquqYonLtjNkJAfw2pCiNy+9VJ/awOApIxvHk2iHbOChnIqdALEGnuPhchS9XcoPExI/KQm0ZvRiQsqo36NjMTMO+VQVrfzYnvkZaFudcRoqYDF4zHYKbpdT5ECgYBaonzAqAkCODKylfHkreAM2J6khhtM+e/kB/m3WxntA/nxeMQU3GiChPW/ii3+S1Z6UvVHOIWUgay9/tScGm/cyke+B50scdWIUFL5M4NpsWXOJwWinRDp6X5l+KtNhq5hpzxHNNa0E+kxGH0ZNaHv6TeARbF1UsT3xWFrjtxjlwKBgD7JXe3zBgjesvDCr9AUIXcoI3e9SNqsBjne6hhNeCaGitoZrN8mZCxTr37J+49evKxyyC7v22885O8XhopT0gBRxMp7Kv4PvFd1p7XiYsP0qhTOvSoGIZCNOgVm5FTpA0HTD14/A69p0Tbjjz+A24PB2joiijW/NqEKgRWIavCN")
        .setProtocol("https")
        .setEndpoint("api.huitongcard.com"));

    @Override
    public CardPlatformEnum getCardPlatform() {
        return CardPlatformEnum.HUI_TONG;
    }

    @Override
    public HuiTongClient getClient() {
        return client;
    }

    @Override
    public List<CardDO> getCardList(LocalDateTime start, LocalDateTime end, Integer syncPage) {
        log.info("======【{}】开始同步卡片数据======", getCardPlatform().getDescription());
        int page = 1;
        int totalPages = 10;
        List<CardDO> cardList = new ArrayList<>();
        while (page <= totalPages) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                HTPaginationRes<HTCard> result = client.getCards(HTGetCardPageRequest.builder()
                    .page(page)
                    .limit(DEFAULT_PAGE_SIZE)
                    .build()).getBody();
                totalPages = result.getPages();
                for (HTCard item : result.getItems()) {
                    CardDO cardDO = new CardDO();
                    cardDO.setCardNumber(item.getCardNumber());
                    cardDO.setPlatform(getCardPlatform());
                    cardDO.setBalance(item.getRemainAmount());
                    cardDO.setExpireDate(item.getExpirationDate());
                    cardDO.setAssociation(item.getAssociation());
                    cardDO.setStatus("正常".equals(item.getStatus()) ? CardStatusEnum.NORMAL : CardStatusEnum.LOCKED);
                    cardDO.setOpenTime(LocalDateTimeUtil.parse(item.getCreatedAt(), "yyyy-MM-dd'T'HH:mm:ssZ"));
                    cardDO.setPlatformCardId(item.getToken());
                    cardList.add(cardDO);
                }
                if (syncPage != null && page == syncPage) {
                    break;
                }
                page++;
                Common.sleep(500);
            } catch (ThirdException e) {
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            }
        }
        log.info("======【{}】卡片数据完成======", getCardPlatform().getDescription());
        return cardList;
    }

    @Override
    public List<CardBalanceDO> getCardBalanceList(LocalDateTime start, LocalDateTime end) {
        log.info("======【{}】开始同步卡片余额流水数据======", getCardPlatform().getDescription());
        List<String> validTypeList = List.of("卡片充值", "卡片自动充值", "提现");
        int page = 1;
        int totalPages = 10;
        List<CardBalanceDO> cardBalanceList = new ArrayList<>();
        while (page <= totalPages) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                HTGetBalanceRecordRequest.HTGetBalanceRecordRequestBuilder builder = HTGetBalanceRecordRequest.builder();
                builder.page(page).limit(DEFAULT_PAGE_SIZE);
                if (start != null && end != null) {
                    builder.start(LocalDateTimeUtil.format(start, "yyyy-MM-dd"));
                    builder.end(LocalDateTimeUtil.format(end, "yyyy-MM-dd"));
                }
                HTPaginationRes<HTBalanceRecord> result = client.getBalanceRecords(builder.build()).getBody();
                totalPages = result.getPages();
                for (HTBalanceRecord item : result.getItems()) {
                    String type = item.getType();
                    if (!validTypeList.contains(type) || StringUtils.isBlank(item.getCardNumber())) {
                        continue;
                    }
                    CardBalanceDO cardBalanceDO = new CardBalanceDO();
                    cardBalanceDO.setPlatform(getCardPlatform());
                    cardBalanceDO.setCardNumber(item.getCardNumber());
                    CardBalanceTypeEnum typeEnum;
                    if (type.equals("提现")) {
                        typeEnum = CardBalanceTypeEnum.WITHDRAW;
                    } else {
                        typeEnum = CardBalanceTypeEnum.RECHARGE;
                    }
                    cardBalanceDO.setType(typeEnum);
                    cardBalanceDO.setOriginType(type);
                    cardBalanceDO.setAmount(item.getAmount().abs());
                    cardBalanceDO.setAfterAmount(item.getNewAmount());
                    cardBalanceDO.setTransTime(LocalDateTimeUtil.parse(item.getCreatedAt(), "yyyy-MM-dd'T'HH:mm:ssZ"));
                    cardBalanceDO.setTransactionId(item.getTransNo());
                    cardBalanceList.add(cardBalanceDO);
                }
                page++;
                Common.sleep(500);
            } catch (ThirdException e) {
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            }
        }
        log.info("======【{}】卡片余额流水数据完成======", getCardPlatform().getDescription());
        return cardBalanceList;
    }

    @Override
    public List<CardTransactionDO> getCardTransactionList(LocalDateTime start, LocalDateTime end, String status) {
        log.info("======【{}】开始同步卡片交易数据======", getCardPlatform().getDescription());
        int page = 1;
        int totalPages = 10;
        if (start == null) {
            start = LocalDateTimeUtil.parse("2024-11-02 00:00:00", "yyyy-MM-dd HH:mm:ss");
        }
        if (end == null) {
            end = LocalDateTimeUtil.now();
        }
        List<CardTransactionDO> list = new ArrayList<>();
        while (page <= totalPages) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            HTGetTransactionsRequest.HTGetTransactionsRequestBuilder builder = HTGetTransactionsRequest.builder();
            builder.page(page)
                .limit(DEFAULT_PAGE_SIZE)
                .start(LocalDateTimeUtil.format(start, "yyyy-MM-dd"))
                .end(LocalDateTimeUtil.format(end, "yyyy-MM-dd"));
            HTPaginationRes<HTTransaction> result = client.getTransactions(builder.build()).getBody();
            totalPages = result.getPages();
            for (HTTransaction item : result.getItems()) {
                CardTransactionDO cardTransactionDO = new CardTransactionDO();
                cardTransactionDO.setPlatform(getCardPlatform());
                cardTransactionDO.setCardNumber(item.getCardNumber());
                cardTransactionDO.setTransactionId(item.getTransactionId());
                cardTransactionDO.setTransType(getCardTransactionType(item.getType()));
                cardTransactionDO.setOriginTransType(item.getType());
                cardTransactionDO.setTransStatus(getCardTransactionStatus(item.getStatusCode()));
                cardTransactionDO.setTransAmount(item.getOriginalAmount());
                cardTransactionDO.setTransCurrency(item.getOriginalCurrencyCode());
                cardTransactionDO.setTransTime(LocalDateTimeUtil.parse(item.getAuthorizedAt(), "yyyy-MM-dd'T'HH:mm:ssZ"));
                cardTransactionDO.setChinaTime(cardTransactionDO.getTransTime());
                cardTransactionDO.setTransDetail(item.getMerchantName());
                list.add(cardTransactionDO);
            }
            page++;
            Common.sleep(500);
        }
        log.info("======【{}】卡片交易数据完成======", getCardPlatform().getDescription());
        return list;
    }

    @Override
    public void rechargeCard(CardDO cardDO, BigDecimal amount) {
        log.info("======【{}】开始充值卡片{}，金额：{}======", getCardPlatform().getDescription(), cardDO.getPlatformCardId(), amount);
        client.rechargeCard(cardDO.getPlatformCardId(), HTRechargeCardRequest.builder().amount(amount).build());
        log.info("======【{}】卡片{}充值完成======", getCardPlatform().getDescription(), cardDO.getPlatformCardId());
    }

    @Override
    public BigDecimal withdrawCard(CardDO cardDO, BigDecimal amount) {
        // 要保留1美金，需要先查询账号限额
        String cardNumber = cardDO.getCardNumber();
        BigDecimal withdrawAmount;
        log.info("======【{}】开始提现卡片{}======", getCardPlatform().getDescription(), cardNumber);
        if (amount == null) {
            HTCard card = client.getCardDetail(cardDO.getPlatformCardId()).getBody();
            log.info("【{}】卡片{}当前余额：{}", getCardPlatform().getDescription(), cardNumber, card.getRemainAmount());
            if (card.getRemainAmount().compareTo(BigDecimal.ONE) <= 0) {
                log.info("【{}】卡片{}小于零，无法提现", getCardPlatform().getDescription(), cardNumber);
                return BigDecimal.ZERO;
            } else {
                withdrawAmount = card.getRemainAmount();
            }
        } else {
            withdrawAmount = amount;
        }
        client.withdrawCard(cardDO.getPlatformCardId(), HTWithdrawCardRequest.builder().amount(withdrawAmount).build());
        log.info("======【{}】卡片{}提现完成======", getCardPlatform().getDescription(), cardNumber);
        return withdrawAmount;
    }

    @Override
    public CardDO openCard(JSONObject data) {
        return null;
    }

    @Override
    public String getVerifyCode(CardDO card) {
        return "";
    }

    @Override
    public void updateRemark(CardDO card) {

    }

    @Override
    public List<LabelValueResp<String>> getCardBinList() {
        return List.of();
    }

    @Override
    public List<CardTransactionDO> convertCardTransactionList(JSONArray jsonArray) {
        return List.of();
    }

    @Override
    public CardDO getCardDetail(String cardId) {
        return null;
    }

    @Override
    public CardDO getCardSensitiveDetail(String cardId) {
        return null;
    }

    @Override
    public CardDO getApplyCardResult(String requestId) {
        return null;
    }

    @Override
    public BigDecimal getCurrentBalance() {
        return null;
    }

    @Override
    public BigDecimal getCardBalance(CardDO cardDO) {
        return null;
    }

    public void activeCard(CardDO cardDO) {

    }

    @Override
    public void inactiveCard(CardDO cardDO) {
    }

    private CardTransactionTypeEnum getCardTransactionType(String type) {
        switch (type) {
            case "消费授权", "授权查询" -> {
                return CardTransactionTypeEnum.AUTHORIZATION;
            }
            case "消费授权冲正" -> {
                return CardTransactionTypeEnum.AUTHORIZATION_BACK;
            }
            case "退款授权" -> {
                return CardTransactionTypeEnum.REFUND;
            }
            default -> {
                return CardTransactionTypeEnum.OTHER;
            }
        }
    }

    private CardTransactionStatusEnum getCardTransactionStatus(String status) {
        switch (status) {
            case "payState.01" -> {
                return CardTransactionStatusEnum.PENDING;
            }
            case "payState.02" -> {
                return CardTransactionStatusEnum.SUCCESS;
            }
            case "payState.04" -> {
                return CardTransactionStatusEnum.FAIL;
            }
            default -> {
                return CardTransactionStatusEnum.OTHER;
            }
        }
    }
}
