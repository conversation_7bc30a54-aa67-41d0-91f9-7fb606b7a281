/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 下户订单查询条件
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
@Data
@Schema(description = "下户订单查询条件")
public class AdAccountOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    @Query(type = QueryType.EQ)
    private String orderNo;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @Query(type = QueryType.EQ, columns = {"o.customer_id"})
    private Long customerId;

    /**
     * 关联广告户
     */
    @Schema(description = "关联广告户")
    @Query(type = QueryType.EQ, columns = {"o.ad_account_id"})
    private String adAccountId;

    /**
     * 客户BM ID
     */
    @Schema(description = "客户BM ID")
    @Query(type = QueryType.LIKE)
    private String customerBmId;

    @Schema(description = "客户邮箱")
    @Query(type = QueryType.LIKE)
    private String customerEmail;

    @Schema(description = "备注")
    @QueryIgnore
    private String remark;

    /**
     * 支付时间
     */
    @Schema(description = "下户时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] payTime;

    @Schema(description = "授权时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] finishTime;

    @Schema(description = "回收时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] recycleTime;

    @Schema(description = "账号状态")
    @Query(type = QueryType.IN, columns = {"a.account_status"})
    private List<Integer> accountStatus;

    @Schema(description = "清零状态")
    @Query(type = QueryType.EQ, columns = {"o.clear_status"})
    private Integer clearStatus;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @Query(type = QueryType.EQ, columns = {"o.status"})
    private Integer status;


    @Schema(description = "BM5 ID")
    @QueryIgnore
    private String adAccountBmIds;


    @Schema(description = "广告户 ID")
    @QueryIgnore
    private String adAccountIds;

    @Schema(description = "处理人")
    @Query(type = QueryType.EQ, columns = {"o.handle_user"})
    private Long handleUser;

    @Schema(description = "时区")
    @Query(type = QueryType.EQ, columns = {"a.timezone"})
    private String timezone;

    @Query(type = QueryType.IN, columns = {"a.bm_item_type"})
    private List<Long> bmType;

    /**
     * 是否已退款
     */
    @Query(type = QueryType.EQ, columns = {"o.refunded"})
    private Boolean refunded;


    @Query(type = QueryType.EQ, columns = {"o.enable_prepay"})
    private Boolean enablePrepay;

    @Query(type = QueryType.EQ, columns = {"o.is_one_dollar"})
    private Boolean isOneDollar;

    @QueryIgnore
    private List<Long> tags;

    @QueryIgnore
    private Boolean isOpen;

    @QueryIgnore
    private Integer campaignTime;

    @Query(type = QueryType.EQ, columns = {"bbmc.name"})
    private String channelName;


    @Query(type = QueryType.BETWEEN, columns = {"a.bm_auth_time"})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] bmAuthTime;


    @Query(type = QueryType.EQ,columns = {"o.take_status"})
    private Boolean takeStatus;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @Query(type = QueryType.EQ, columns = {"c.business_type"})
    private Integer customerBusinessType;

}